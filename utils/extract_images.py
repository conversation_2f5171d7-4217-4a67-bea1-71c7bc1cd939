import os
import pickle
import shutil
import numpy as np
from pathlib import Path
from tqdm import tqdm
from pycocotools.coco import COCO
import cv2

# 定义COCO数据集中的动物类别ID
ANIMAL_CATEGORY_IDS = {
    16: 'bird',
    17: 'cat', 
    18: 'dog',
    19: 'horse',
    20: 'sheep',
    21: 'cow',
    22: 'elephant',
    23: 'bear',
    24: 'zebra',
    25: 'giraffe'
}

class AnimalDatabaseBuilder:
    """动物轮廓数据库生成器"""
    
    def __init__(self, coco_dir, output_dir):
        """初始化数据库生成器
        
        Args:
            coco_dir: COCO数据集根目录
            output_dir: 输出目录
        """
        self.coco_dir = Path(coco_dir)
        self.output_dir = Path(output_dir)
        self.database_dir = self.output_dir / 'database'
        self.images_dir = self.database_dir / 'images'
        self.images_dir.mkdir(exist_ok=True, parents=True)
        
        # 数据库结构
        self.database = {
            'contours': [],      # 轮廓数据
            'image_ids': [],     # 图片ID
            'categories': [],    # 类别名称
            'image_paths': []    # 图片路径
        }
        
        # 统计信息
        self.stats = {
            'total_images': 0,
            'animal_instances': 0,
            'category_counts': {}
        }
    
    def load_coco_dataset(self, annotation_file='instances_val2017.json'):
        """加载COCO数据集
        
        Args:
            annotation_file: 标注文件名
        
        Returns:
            COCO对象
        """
        print(f"加载COCO数据集: {annotation_file}")
        ann_path = self.coco_dir / 'annotations' / annotation_file
        if not ann_path.exists():
            raise FileNotFoundError(f"找不到标注文件: {ann_path}")
        return COCO(str(ann_path))
    
    def extract_animal_contours(self, coco, image_dir='val2017'):
        """从COCO数据集中提取动物轮廓
        
        Args:
            coco: COCO对象
            image_dir: 图片目录名
        """
        print("筛选动物类别...")
        
        # 筛选动物类别的图片
        for category_id, category_name in ANIMAL_CATEGORY_IDS.items():
            # 获取该类别的所有图片ID
            img_ids = coco.getImgIds(catIds=[category_id])
            self.stats['total_images'] += len(img_ids)
            
            print(f"处理 {category_name} 类别: {len(img_ids)} 张图片")
            
            # 处理每张图片
            for img_id in tqdm(img_ids):
                # 获取图片信息
                img_info = coco.loadImgs(img_id)[0]
                
                # 获取图片的所有标注
                ann_ids = coco.getAnnIds(imgIds=img_id, catIds=[category_id], iscrowd=False)
                annotations = coco.loadAnns(ann_ids)
                
                # 处理每个标注
                for ann in annotations:
                    # 提取分割掩码
                    mask = coco.annToMask(ann)
                    
                    # 找到轮廓
                    contours, _ = cv2.findContours(mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
                    
                    if contours:
                        # 获取最大轮廓
                        max_contour = max(contours, key=cv2.contourArea)
                        
                        # 标准化轮廓
                        x, y, w, h = cv2.boundingRect(max_contour)
                        if w <= 0 or h <= 0:
                            continue  # 跳过无效轮廓
                            
                        max_contour = max_contour - [x, y]
                        scale = 100.0 / max(w, h)
                        normalized_contour = (max_contour * scale).astype(np.float32)
                        
                        # 复制图片
                        src_path = self.coco_dir / image_dir / img_info['file_name']
                        dst_path = self.images_dir / f"{img_id}.jpg"
                        
                        if src_path.exists() and not dst_path.exists():
                            shutil.copy(str(src_path), str(dst_path))
                        
                        if dst_path.exists():
                            # 添加到数据库
                            self.database['contours'].append(normalized_contour)
                            self.database['image_ids'].append(str(img_id))
                            self.database['categories'].append(category_name)
                            self.database['image_paths'].append(f"{img_id}.jpg")
                            
                            # 更新统计信息
                            self.stats['animal_instances'] += 1
                            self.stats['category_counts'][category_name] = \
                                self.stats['category_counts'].get(category_name, 0) + 1
    
    def save_database(self):
        """保存数据库"""
        print("\n保存数据库...")
        database_path = self.database_dir / 'animal_contours.pkl'
        with open(database_path, 'wb') as f:
            pickle.dump(self.database, f)
        print(f"数据库已保存到: {database_path}")
    
    def print_statistics(self):
        """打印统计信息"""
        print("\n数据库统计:")
        print(f"总图片数: {self.stats['total_images']}")
        print(f"动物实例数: {self.stats['animal_instances']}")
        
        print("\n类别统计:")
        for cat, count in sorted(self.stats['category_counts'].items(), key=lambda x: x[1], reverse=True):
            print(f"{cat}: {count} 个实例")
    
    def build_database(self):
        """构建动物轮廓数据库"""
        try:
            # 加载COCO数据集
            coco = self.load_coco_dataset()
            
            # 提取动物轮廓
            self.extract_animal_contours(coco)
            
            # 保存数据库
            self.save_database()
            
            # 打印统计信息
            self.print_statistics()
            
            return True
        except Exception as e:
            print(f"构建数据库时出错: {e}")
            return False

def main():
    """主函数"""
    # 设置路径
    coco_dir = '/Users/<USER>/Desktop/cocodataset'
    output_dir = '/Users/<USER>/Desktop/pose_matching_project'
    
    # 构建数据库
    builder = AnimalDatabaseBuilder(coco_dir, output_dir)
    success = builder.build_database()
    
    if success:
        print("\n成功构建动物轮廓数据库!")
    else:
        print("\n构建数据库失败!")

if __name__ == '__main__':
    main() 