import os
import sys
import requests
from pathlib import Path
from tqdm import tqdm

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

from config import MODELS_DIR

def download_file_from_drive(file_id: str, output_path: Path) -> None:
    """
    从Google Drive下载文件
    """
    def get_confirm_token(response):
        for key, value in response.cookies.items():
            if key.startswith('download_warning'):
                return value
        return None

    def save_response_content(response, destination):
        CHUNK_SIZE = 32768
        total_size = int(response.headers.get('content-length', 0))
        
        with open(destination, "wb") as f, tqdm(
            desc=destination.name,
            total=total_size,
            unit='iB',
            unit_scale=True,
            unit_divisor=1024,
        ) as pbar:
            for chunk in response.iter_content(CHUNK_SIZE):
                if chunk:
                    f.write(chunk)
                    pbar.update(len(chunk))

    URL = "https://docs.google.com/uc?export=download"
    session = requests.Session()
    response = session.get(URL, params={'id': file_id}, stream=True)
    token = get_confirm_token(response)

    if token:
        params = {'id': file_id, 'confirm': token}
        response = session.get(URL, params=params, stream=True)
    
    save_response_content(response, output_path)

def main():
    # 创建模型目录
    MODELS_DIR.mkdir(parents=True, exist_ok=True)
    
    # 您的模型文件ID
    model_file_id = "15cF_ct65rBKgs8etxX6NyWrWBFGWqIMg"
    output_path = MODELS_DIR / "human_seg_model.pt"
    
    print("开始下载模型...")
    download_file_from_drive(model_file_id, output_path)
    print(f"模型已保存到: {output_path}")

if __name__ == "__main__":
    main() 