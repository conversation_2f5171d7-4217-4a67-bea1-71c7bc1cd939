import os
import json
import requests
from pathlib import Path
from tqdm import tqdm
from pycocotools.coco import COCO

class COCODownloader:
    def __init__(self):
        self.base_dir = Path(__file__).parent.parent
        self.data_dir = self.base_dir / 'data' / 'coco'
        self.annotations_dir = self.data_dir / 'annotations'
        self.images_dir = self.data_dir / 'val2017'
        
        # COCO数据集URL
        self.urls = {
            'annotations': 'http://images.cocodataset.org/annotations/annotations_trainval2017.zip',
            'val2017': 'http://images.cocodataset.org/zips/val2017.zip'
        }
        
        # 目标动物类别
        self.target_categories = [
            'dog', 'cat', 'horse', 'sheep', 'cow',
            'elephant', 'bear', 'zebra', 'giraffe', 'bird'
        ]

    def download_file(self, url: str, save_path: Path):
        """下载文件并显示进度条"""
        if save_path.exists():
            print(f"文件已存在: {save_path}")
            return
        
        response = requests.get(url, stream=True)
        total_size = int(response.headers.get('content-length', 0))
        
        save_path.parent.mkdir(parents=True, exist_ok=True)
        
        with open(save_path, 'wb') as f:
            with tqdm(total=total_size, unit='B', unit_scale=True) as pbar:
                for data in response.iter_content(chunk_size=1024):
                    f.write(data)
                    pbar.update(len(data))

    def extract_zip(self, zip_path: Path):
        """解压ZIP文件"""
        import zipfile
        print(f"解压文件: {zip_path}")
        with zipfile.ZipFile(zip_path, 'r') as zip_ref:
            zip_ref.extractall(self.data_dir)

    def download_dataset(self):
        """下载COCO数据集"""
        print("开始下载COCO数据集...")
        
        # 下载并解压标注文件
        annotations_zip = self.data_dir / 'annotations.zip'
        self.download_file(self.urls['annotations'], annotations_zip)
        self.extract_zip(annotations_zip)
        
        # 下载并解压验证集图片
        val_zip = self.data_dir / 'val2017.zip'
        self.download_file(self.urls['val2017'], val_zip)
        self.extract_zip(val_zip)

    def filter_animal_annotations(self):
        """筛选动物类别的图片和标注"""
        print("开始筛选动物标注...")
        
        # 加载COCO API
        coco = COCO(str(self.annotations_dir / 'instances_val2017.json'))
        
        # 获取目标类别的ID
        cat_ids = []
        for cat_name in self.target_categories:
            cat_id = coco.getCatIds(catNms=[cat_name])
            if cat_id:
                cat_ids.extend(cat_id)
        
        # 获取包含目标类别的图片
        img_ids = []
        for cat_id in cat_ids:
            img_ids.extend(coco.getImgIds(catIds=[cat_id]))
        img_ids = list(set(img_ids))  # 去重
        
        # 保存筛选后的标注
        filtered_annotations = {
            'images': [coco.loadImgs(img_id)[0] for img_id in img_ids],
            'categories': [cat for cat in coco.loadCats(cat_ids)],
            'annotations': []
        }
        
        for img_id in img_ids:
            ann_ids = coco.getAnnIds(imgIds=img_id, catIds=cat_ids)
            filtered_annotations['annotations'].extend(coco.loadAnns(ann_ids))
        
        # 保存筛选后的标注文件
        output_file = self.data_dir / 'filtered_annotations.json'
        with open(output_file, 'w') as f:
            json.dump(filtered_annotations, f)
        
        print(f"已保存筛选后的标注到: {output_file}")
        print(f"共筛选出 {len(img_ids)} 张图片")

def main():
    downloader = COCODownloader()
    downloader.download_dataset()
    downloader.filter_animal_annotations()

if __name__ == '__main__':
    main() 