import cv2
import numpy as np
from typing import List, <PERSON><PERSON>

def extract_contours(mask: np.ndarray) -> List[np.ndarray]:
    """
    从掩码中提取轮廓
    """
    contours, _ = cv2.findContours(mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
    return sorted(contours, key=cv2.contourArea, reverse=True)

def normalize_contour(contour: np.ndarray) -> np.ndarray:
    """
    标准化轮廓（中心对齐和大小归一化）
    """
    # 计算轮廓的边界框
    x, y, w, h = cv2.boundingRect(contour)
    
    # 移动轮廓到原点
    centered_contour = contour - [x + w/2, y + h/2]
    
    # 计算缩放因子
    scale = max(w, h)
    if scale > 0:
        normalized_contour = centered_contour / scale
    else:
        normalized_contour = centered_contour
    
    return normalized_contour

def calculate_similarity(contour1: np.ndarray, contour2: np.ndarray) -> float:
    """
    计算两个轮廓之间的相似度
    """
    # 使用Hu矩来计算相似度
    similarity = cv2.matchShapes(contour1, contour2, cv2.CONTOURS_MATCH_I2, 0.0)
    # 转换为0-1范围的相似度分数（值越小表示越相似）
    similarity_score = 1 / (1 + similarity)
    return similarity_score

def draw_contours(image: np.ndarray, contours: List[np.ndarray], color: Tuple[int, int, int] = (0, 255, 0)) -> np.ndarray:
    """
    在图像上绘制轮廓
    """
    result = image.copy()
    cv2.drawContours(result, contours, -1, color, 2)
    return result

if __name__ == "__main__":
    # 测试代码
    test_mask = np.zeros((100, 100), dtype=np.uint8)
    cv2.rectangle(test_mask, (25, 25), (75, 75), 255, -1)
    contours = extract_contours(test_mask)
    print(f"Found {len(contours)} contours") 