import cv2
import numpy as np
import time
from pathlib import Path
from contour_retrieval import ContourRetrieval

class CameraRetrieval:
    """相机检索系统"""
    
    def __init__(self):
        """初始化相机检索系统"""
        self.retrieval = ContourRetrieval()
        self.cap = None
        self.temp_dir = Path(__file__).parent.parent / 'temp'
        self.temp_dir.mkdir(exist_ok=True)
        
    def countdown(self, frame):
        """显示倒计时"""
        for i in range(3, 0, -1):
            # 在画面中央显示倒计时数字
            display_frame = frame.copy()
            cv2.putText(display_frame, str(i), (frame.shape[1]//2 - 50, frame.shape[0]//2), 
                       cv2.FONT_HERSHEY_SIMPLEX, 4, (0, 0, 255), 5)
            cv2.imshow('相机预览', display_frame)
            cv2.waitKey(1000)  # 等待1秒
            ret, frame = self.cap.read()  # 更新画面
            if not ret:
                break
        
        # 显示"拍照"文字
        if frame is not None:
            display_frame = frame.copy()
            cv2.putText(display_frame, "拍照!", (frame.shape[1]//2 - 100, frame.shape[0]//2), 
                       cv2.FONT_HERSHEY_SIMPLEX, 2, (0, 255, 0), 3)
            cv2.imshow('相机预览', display_frame)
            cv2.waitKey(500)  # 等待0.5秒
            return frame
        return None
        
    def capture_and_retrieve(self):
        """打开相机，拍照并检索相似姿势"""
        print("启动相机...")
        self.cap = cv2.VideoCapture(0)
        
        if not self.cap.isOpened():
            print("无法打开相机")
            return
        
        try:
            while True:
                ret, frame = self.cap.read()
                if not ret:
                    print("无法获取画面")
                    break
                    
                # 显示指导信息
                display_frame = frame.copy()
                cv2.putText(display_frame, "按空格键开始倒计时", (50, 50), 
                           cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 0), 2)
                cv2.putText(display_frame, "按'q'键退出", (50, 100), 
                           cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 0), 2)
                
                cv2.imshow('相机预览', display_frame)
                key = cv2.waitKey(1) & 0xFF
                
                if key == ord('q'):
                    break
                elif key == ord(' '):
                    # 开始倒计时
                    captured_frame = self.countdown(frame)
                    
                    # 拍照
                    if captured_frame is not None:
                        # 保存临时图片
                        temp_image = self.temp_dir / 'capture.jpg'
                        cv2.imwrite(str(temp_image), captured_frame)
                        
                        # 进行检索
                        print("\n开始检索...")
                        try:
                            results = self.retrieval.retrieve_similar_poses(str(temp_image))
                            
                            # 显示结果
                            print("\n检索结果:")
                            for i, result in enumerate(results, 1):
                                print(f"{i}. 类别: {result['category']}, 相似度: {result['similarity_score']:.2f}")
                            
                            # 读取结果图片并显示
                            result_image = cv2.imread(str(self.retrieval.results_dir / f"retrieval_results_capture.png"))
                            if result_image is not None:
                                # 创建可调整大小的窗口
                                cv2.namedWindow('检索结果', cv2.WINDOW_NORMAL)
                                cv2.resizeWindow('检索结果', 1200, 600)  # 为2行显示调整大小
                                cv2.imshow('检索结果', result_image)
                                
                                print("\n按任意键继续...")
                                cv2.waitKey(0)
                                cv2.destroyWindow('检索结果')
                            
                        except Exception as e:
                            print(f"检索过程中出错: {e}")
                            
        finally:
            # 释放资源
            self.cap.release()
            cv2.destroyAllWindows()

def main():
    """主函数"""
    try:
        camera = CameraRetrieval()
        camera.capture_and_retrieve()
    except Exception as e:
        print(f"程序出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    main() 