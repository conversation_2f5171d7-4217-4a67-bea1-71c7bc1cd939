import os
import requests
import zipfile
from tqdm import tqdm
from pathlib import Path
from config import COCO_DIR, COCO_URLS

def download_file(url: str, save_path: Path) -> None:
    """
    下载文件并显示进度条
    """
    response = requests.get(url, stream=True)
    total_size = int(response.headers.get('content-length', 0))
    
    with open(save_path, 'wb') as file, tqdm(
        desc=save_path.name,
        total=total_size,
        unit='iB',
        unit_scale=True,
        unit_divisor=1024,
    ) as pbar:
        for data in response.iter_content(chunk_size=1024):
            size = file.write(data)
            pbar.update(size)

def extract_zip(zip_path: Path, extract_path: Path) -> None:
    """
    解压文件并显示进度条
    """
    with zipfile.ZipFile(zip_path, 'r') as zip_ref:
        for file in tqdm(zip_ref.namelist(), desc=f"Extracting {zip_path.name}"):
            zip_ref.extract(file, extract_path)

def setup_coco_dataset() -> None:
    """
    下载并设置COCO数据集
    """
    COCO_DIR.mkdir(parents=True, exist_ok=True)
    
    # 下载并解压验证集图片
    val_zip = COCO_DIR / "val2017.zip"
    if not val_zip.exists():
        print("Downloading validation images...")
        download_file(COCO_URLS["val_images"], val_zip)
    
    if not (COCO_DIR / "val2017").exists():
        print("Extracting validation images...")
        extract_zip(val_zip, COCO_DIR)
    
    # 下载并解压标注文件
    ann_zip = COCO_DIR / "annotations.zip"
    if not ann_zip.exists():
        print("Downloading annotations...")
        download_file(COCO_URLS["annotations"], ann_zip)
    
    if not (COCO_DIR / "annotations").exists():
        print("Extracting annotations...")
        extract_zip(ann_zip, COCO_DIR)

if __name__ == "__main__":
    setup_coco_dataset() 