import os
import cv2
import numpy as np
import pickle
import torch
from pathlib import Path
import matplotlib.pyplot as plt
from ultralytics import YOLO
from scipy.spatial import KDTree

class ContourRetrieval:
    """轮廓检索系统"""
    
    def __init__(self):
        """初始化检索系统"""
        self.base_dir = Path(__file__).parent.parent
        self.database_path = self.base_dir / 'database' / 'animal_contours.pkl'
        self.human_model_path = self.base_dir / 'models' / 'human_seg_model.pt'
        self.results_dir = self.base_dir / 'results'
        self.results_dir.mkdir(exist_ok=True)
        
        # 加载人体分割模型
        print("加载人体分割模型...")
        self.human_model = YOLO(self.human_model_path)
        
        # 加载动物轮廓数据库
        print("加载动物数据库...")
        with open(self.database_path, 'rb') as f:
            self.database = pickle.load(f)
            
        # 构建KD树索引
        print("构建检索索引...")
        self.build_index()
        
        print("初始化完成！")
    
    def build_index(self):
        """构建KD树索引用于快速检索"""
        # 为每个轮廓计算特征
        self.features = []
        for contour in self.database['contours']:
            features = self.compute_shape_features(contour)
            self.features.append(features)
        
        # 将特征列表转换为numpy数组
        self.features = np.array(self.features)
        
        # 处理无穷值和NaN值
        self.features = np.nan_to_num(self.features, nan=0.0, posinf=0.0, neginf=0.0)
        
        # 构建KD树
        self.kdtree = KDTree(self.features)
    
    def compute_shape_features(self, contour):
        """计算轮廓的形状特征"""
        # 计算Hu矩
        moments = cv2.moments(contour)
        if moments['m00'] != 0:
            hu_moments = cv2.HuMoments(moments).flatten()
        else:
            hu_moments = np.zeros(7)
        
        # 安全的对数变换
        log_hu = np.zeros_like(hu_moments)
        for i, hu in enumerate(hu_moments):
            if abs(hu) > 1e-7:  # 避免对0取对数
                log_hu[i] = -np.sign(hu) * np.log10(abs(hu))
            else:
                log_hu[i] = 0
        
        # 计算面积和周长比
        area = cv2.contourArea(contour)
        perimeter = cv2.arcLength(contour, True)
        if perimeter > 0:
            circularity = 4 * np.pi * area / (perimeter * perimeter)
        else:
            circularity = 0
        
        # 计算轮廓方向
        if len(contour) >= 5:  # 至少需要5个点来拟合椭圆
            try:
                (x, y), (ma, mi), angle = cv2.fitEllipse(contour)
                aspect_ratio = ma / mi if mi > 0 else 1.0
            except:
                aspect_ratio = 1.0
                angle = 0
        else:
            aspect_ratio = 1.0
            angle = 0
        
        # 组合特征
        features = np.concatenate([
            log_hu,  # Hu矩
            [circularity],  # 圆度
            [aspect_ratio],  # 长宽比
            [np.sin(angle * np.pi / 180), np.cos(angle * np.pi / 180)]  # 方向（正弦和余弦）
        ])
        
        # 处理无穷值和NaN值
        features = np.nan_to_num(features, nan=0.0, posinf=0.0, neginf=0.0)
        
        return features
    
    def extract_human_contour(self, image_path):
        """从人体图片中提取轮廓"""
        # 使用YOLO模型进行预测
        results = self.human_model(image_path, verbose=False)
        
        # 获取分割掩码
        if not results or not results[0].masks:
            raise ValueError("未检测到人体轮廓")
        
        # 获取人体掩码（选择置信度最高的人体检测结果）
        person_masks = []
        for r in results[0].boxes.data:
            if int(r[5]) == 0:  # COCO数据集中0表示人
                mask_idx = int(r[4])  # 获取对应的掩码索引
                if mask_idx < len(results[0].masks):
                    person_masks.append((float(r[4]), results[0].masks[mask_idx].data[0].cpu().numpy()))
        
        if not person_masks:
            raise ValueError("未检测到人体轮廓")
        
        # 使用置信度最高的人体掩码
        mask = max(person_masks, key=lambda x: x[0])[1]
        
        # 提取轮廓
        mask = (mask * 255).astype(np.uint8)
        contours, _ = cv2.findContours(mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        if not contours:
            raise ValueError("未检测到人体轮廓")
        
        # 获取最大轮廓
        max_contour = max(contours, key=cv2.contourArea)
        
        # 标准化轮廓
        x, y, w, h = cv2.boundingRect(max_contour)
        max_contour = max_contour - [x, y]
        scale = 100.0 / max(w, h)
        max_contour = (max_contour * scale).astype(np.float32)
        
        return max_contour
    
    def retrieve_similar_poses(self, image_path, top_k=5):
        """检索相似姿势的动物"""
        # 提取人体轮廓
        print("提取人体轮廓...")
        human_contour = self.extract_human_contour(image_path)
        
        # 计算特征
        print("计算特征...")
        human_features = self.compute_shape_features(human_contour)
        
        # 在KD树中搜索最近邻
        print("搜索相似姿势...")
        distances, indices = self.kdtree.query(human_features, k=top_k)
        
        # 准备结果
        results = []
        for dist, idx in zip(distances, indices):
            results.append({
                'category': self.database['categories'][idx],
                'image_id': self.database['image_ids'][idx],
                'image_path': self.database['image_paths'][idx],
                'contour': self.database['contours'][idx],
                'similarity_score': 1.0 / (1.0 + dist)  # 将距离转换为相似度分数
            })
        
        # 可视化结果
        self.visualize_results(image_path, human_contour, results)
        
        return results
    
    def visualize_results(self, image_path, human_contour, results):
        """可视化检索结果"""
        n_results = len(results)
        fig, axes = plt.subplots(2, n_results + 1, figsize=(4*(n_results + 1), 8))
        fig.patch.set_facecolor('white')
        
        # 显示输入图片
        query_img = cv2.imread(str(image_path))
        query_img = cv2.cvtColor(query_img, cv2.COLOR_BGR2RGB)
        axes[0, 0].imshow(query_img)
        axes[0, 0].axis('off')
        axes[0, 0].set_title('输入图片', fontsize=12, pad=10)
        
        # 显示人体轮廓
        img_human = np.zeros((200, 200, 3), dtype=np.uint8)
        img_human.fill(255)
        human_contour_centered = human_contour + [100, 100]
        cv2.drawContours(img_human, [human_contour_centered.astype(np.int32)], -1, (0, 0, 0), 2)
        axes[1, 0].imshow(img_human)
        axes[1, 0].axis('off')
        axes[1, 0].set_title('人体轮廓', fontsize=12, pad=10)
        
        # 显示检索结果
        for i, result in enumerate(results):
            # 显示动物图片
            img_path = self.base_dir / 'database' / 'images' / result['image_path']
            if img_path.exists():
                img = cv2.imread(str(img_path))
                if img is not None:
                    img = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)
                    # 调整图片大小以适应显示
                    h, w = img.shape[:2]
                    aspect_ratio = w / h
                    new_h = 200
                    new_w = int(new_h * aspect_ratio)
                    img = cv2.resize(img, (new_w, new_h))
                    axes[0, i+1].imshow(img)
                else:
                    axes[0, i+1].text(0.5, 0.5, '图片不存在', ha='center', va='center')
            else:
                axes[0, i+1].text(0.5, 0.5, '图片不存在', ha='center', va='center')
            
            # 显示动物类别和相似度
            title = f"{result['category']}\n相似度: {result['similarity_score']:.2f}"
            axes[0, i+1].set_title(title, fontsize=12, pad=10)
            axes[0, i+1].axis('off')
            
            # 显示动物轮廓
            img_contour = np.zeros((200, 200, 3), dtype=np.uint8)
            img_contour.fill(255)
            contour = result['contour'] + [100, 100]
            cv2.drawContours(img_contour, [contour.astype(np.int32)], -1, (0, 0, 0), 2)
            axes[1, i+1].imshow(img_contour)
            axes[1, i+1].axis('off')
            axes[1, i+1].set_title('动物轮廓', fontsize=12, pad=10)
        
        plt.tight_layout()
        
        # 保存结果
        output_path = self.results_dir / f"retrieval_results_{Path(image_path).stem}.png"
        plt.savefig(output_path, bbox_inches='tight', dpi=150)
        plt.close()
        
        print(f"结果已保存至: {output_path}")

def main():
    """主函数：简单的测试检索系统"""
    retrieval = ContourRetrieval()
    
    # 加载测试图片（如果有）
    test_image = Path(__file__).parent.parent / 'test_image.jpg'
    if test_image.exists():
        results = retrieval.retrieve_similar_poses(str(test_image))
        print("\n检索结果:")
        for i, result in enumerate(results, 1):
            print(f"{i}. 类别: {result['category']}, 相似度: {result['similarity_score']:.2f}")
    else:
        print(f"测试图片不存在，请将测试图片放在 {test_image} 位置")

if __name__ == '__main__':
    main() 