import json
import cv2
import numpy as np
from pathlib import Path
from tqdm import tqdm
from ultralytics import YOLO
import pickle

class ContourDatabaseBuilder:
    def __init__(self):
        self.base_dir = Path(__file__).parent.parent
        self.data_dir = self.base_dir / 'data' / 'coco'
        self.database_dir = self.base_dir / 'database'
        self.database_dir.mkdir(exist_ok=True)
        
        # 加载YOLOv8分割模型
        self.model = YOLO('yolov8x-seg.pt')
        
        # 加载筛选后的标注
        with open(self.data_dir / 'filtered_annotations.json', 'r') as f:
            self.annotations = json.load(f)

    def extract_contour(self, mask):
        """从mask中提取轮廓"""
        # 将mask转换为uint8类型
        mask = (mask * 255).astype(np.uint8)
        
        # 查找轮廓
        contours, _ = cv2.findContours(mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        # 获取最大轮廓
        if contours:
            max_contour = max(contours, key=cv2.contourArea)
            return max_contour
        return None

    def normalize_contour(self, contour):
        """标准化轮廓（大小和位置）"""
        if contour is None:
            return None
            
        # 计算边界框
        x, y, w, h = cv2.boundingRect(contour)
        
        # 移动轮廓到原点
        contour = contour - [x, y]
        
        # 计算缩放因子（统一大小为100x100）
        scale = 100.0 / max(w, h)
        
        # 缩放轮廓
        contour = (contour * scale).astype(np.float32)
        
        return contour

    def build_database(self):
        """构建动物轮廓数据库"""
        print("开始构建动物轮廓数据库...")
        
        database = {
            'contours': [],  # 存储轮廓
            'image_ids': [],  # 对应的图片ID
            'categories': []  # 对应的类别
        }
        
        # 遍历所有图片
        for img_info in tqdm(self.annotations['images']):
            img_id = img_info['id']
            img_path = self.data_dir / 'val2017' / img_info['file_name']
            
            # 如果图片不存在，跳过
            if not img_path.exists():
                continue
            
            # 使用YOLOv8进行分割
            results = self.model(str(img_path), conf=0.5)
            
            for result in results:
                # 获取所有mask和对应的类别
                if result.masks is None:
                    continue
                    
                masks = result.masks.data.cpu().numpy()
                classes = result.boxes.cls.cpu().numpy()
                
                for mask, cls in zip(masks, classes):
                    # 提取轮廓
                    contour = self.extract_contour(mask)
                    if contour is None:
                        continue
                    
                    # 标准化轮廓
                    norm_contour = self.normalize_contour(contour)
                    if norm_contour is None:
                        continue
                    
                    # 获取类别名称
                    category = result.names[int(cls)]
                    
                    # 保存到数据库
                    database['contours'].append(norm_contour)
                    database['image_ids'].append(img_id)
                    database['categories'].append(category)
        
        # 保存数据库
        output_file = self.database_dir / 'animal_contours.pkl'
        with open(output_file, 'wb') as f:
            pickle.dump(database, f)
        
        print(f"数据库构建完成！共处理 {len(database['contours'])} 个轮廓")
        print(f"数据库保存至: {output_file}")

def main():
    builder = ContourDatabaseBuilder()
    builder.build_database()

if __name__ == '__main__':
    main() 