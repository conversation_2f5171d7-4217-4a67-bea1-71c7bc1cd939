import pickle
import cv2
import numpy as np
from pathlib import Path
from collections import Counter
import matplotlib.pyplot as plt

class DatabaseChecker:
    def __init__(self):
        self.base_dir = Path(__file__).parent.parent
        self.database_path = self.base_dir / 'database' / 'animal_contours.pkl'
        self.output_dir = self.base_dir / 'database' / 'visualization'
        self.output_dir.mkdir(exist_ok=True)
        
        # 目标动物类别
        self.target_categories = {
            'bird', 'cat', 'dog', 'horse', 'sheep', 
            'cow', 'elephant', 'bear', 'zebra', 'giraffe'
        }
        
        # 加载数据库
        print(f"正在加载数据库: {self.database_path}")
        with open(self.database_path, 'rb') as f:
            db = pickle.load(f)
            
        # 只保留动物类别
        self.database = {
            'contours': [],
            'categories': [],
            'image_ids': []
        }
        
        for contour, category, img_id in zip(db['contours'], db['categories'], db['image_ids']):
            if category in self.target_categories:
                self.database['contours'].append(contour)
                self.database['categories'].append(category)
                self.database['image_ids'].append(img_id)
            
        print(f"数据库加载完成！")
        print(f"总轮廓数: {len(self.database['contours'])}")
    
    def analyze_categories(self):
        """分析类别分布"""
        print("\n类别分布分析:")
        category_counts = Counter(self.database['categories'])
        
        # 打印类别统计
        for category, count in category_counts.most_common():
            percentage = count / len(self.database['categories']) * 100
            print(f"{category}: {count} ({percentage:.1f}%)")
            
        # 绘制类别分布柱状图
        plt.figure(figsize=(12, 6))
        categories, counts = zip(*category_counts.most_common())
        plt.bar(categories, counts)
        plt.xticks(rotation=45, ha='right')
        plt.title('动物类别分布')
        plt.xlabel('类别')
        plt.ylabel('数量')
        plt.tight_layout()
        plt.savefig(self.output_dir / 'category_distribution.png')
        plt.close()
    
    def analyze_contour_statistics(self):
        """分析轮廓统计信息"""
        print("\n轮廓统计分析:")
        
        # 计算轮廓点数量
        point_counts = [len(contour) for contour in self.database['contours']]
        
        print(f"平均轮廓点数: {np.mean(point_counts):.1f}")
        print(f"最小轮廓点数: {np.min(point_counts)}")
        print(f"最大轮廓点数: {np.max(point_counts)}")
        
        # 绘制轮廓点数分布直方图
        plt.figure(figsize=(10, 6))
        plt.hist(point_counts, bins=50)
        plt.title('轮廓点数分布')
        plt.xlabel('点数')
        plt.ylabel('频率')
        plt.savefig(self.output_dir / 'contour_points_distribution.png')
        plt.close()
    
    def visualize_sample_contours(self, samples_per_category=2):
        """可视化每个类别的样本轮廓"""
        print("\n正在生成样本轮廓可视化...")
        
        # 按类别组织轮廓
        category_contours = {}
        for contour, category in zip(self.database['contours'], self.database['categories']):
            if category not in category_contours:
                category_contours[category] = []
            category_contours[category].append(contour)
        
        # 为每个类别创建可视化
        for category, contours in category_contours.items():
            # 选择样本
            sample_indices = np.random.choice(len(contours), 
                                           min(samples_per_category, len(contours)), 
                                           replace=False)
            
            fig, axes = plt.subplots(1, len(sample_indices), figsize=(6*len(sample_indices), 6))
            if len(sample_indices) == 1:
                axes = [axes]
            
            for i, idx in enumerate(sample_indices):
                # 创建空白图像
                img = np.zeros((200, 200), dtype=np.uint8)
                
                # 绘制轮廓
                contour = contours[idx]
                # 移动轮廓到图像中心
                contour = contour + [100, 100]
                cv2.drawContours(img, [contour.astype(np.int32)], -1, 255, 2)
                
                # 显示图像
                axes[i].imshow(img, cmap='gray')
                axes[i].axis('off')
                axes[i].set_title(f'{category} - 样本 {i+1}')
            
            plt.tight_layout()
            plt.savefig(self.output_dir / f'sample_contours_{category}.png')
            plt.close()
    
    def check_database(self):
        """运行所有检查"""
        self.analyze_categories()
        self.analyze_contour_statistics()
        self.visualize_sample_contours()
        print(f"\n检查完成！可视化结果保存在: {self.output_dir}")

def main():
    checker = DatabaseChecker()
    checker.check_database()

if __name__ == '__main__':
    main() 