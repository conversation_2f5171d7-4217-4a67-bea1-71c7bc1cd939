from pathlib import Path
import cv2
import numpy as np
import torch
from torch.nn import Sequential, Conv2d, BatchNorm2d, SiL<PERSON>, Module
from ultralytics import YOL<PERSON>
from ultralytics.nn.tasks import SegmentationModel
from ultralytics.nn.modules.conv import Conv
from config import MODELS_DIR, YOLO_CONF_THRESHOLD

# 添加安全全局变量
torch.serialization.add_safe_globals([
    SegmentationModel,
    Sequential,
    Conv2d,
    BatchNorm2d,
    SiLU,
    Module,
    Conv
])

def load_human_model(model_path: str) -> torch.nn.Module:
    """
    加载人类检测模型
    """
    # 直接加载模型
    model = torch.load(model_path, weights_only=False)
    if isinstance(model, dict) and 'model' in model:
        model = model['model']
    return model

def load_animal_model(model_name: str = "yolov8x-seg.pt") -> YOLO:
    """
    加载动物检测模型
    """
    model_path = MODELS_DIR / model_name
    if not model_path.exists():
        print(f"Downloading {model_name}...")
        model = YOLO(model_name)
        model.save(model_path)
    else:
        model = YOLO(model_path)
    return model

def get_segmentation_mask(model: torch.nn.Module, image_path: str, class_id: int = None) -> tuple:
    """
    获取图像分割掩码
    """
    # 读取图像
    image = cv2.imread(image_path)
    if image is None:
        return None, None
    
    # 预处理图像
    input_image = cv2.resize(image, (640, 640))
    input_tensor = torch.from_numpy(input_image.transpose(2, 0, 1)).float().div(255.0).unsqueeze(0)
    
    # 使用模型进行预测
    model.eval()
    with torch.no_grad():
        results = model(input_tensor)
    
    # 处理预测结果
    if isinstance(results, (list, tuple)):
        results = results[0]
    
    # 获取掩码
    if hasattr(results, 'masks') and results.masks is not None:
        mask = results.masks[0].data.cpu().numpy()[0]
        mask = (mask * 255).astype(np.uint8)
        mask = cv2.resize(mask, (image.shape[1], image.shape[0]))
        return mask, image
    
    return None, image

if __name__ == "__main__":
    # 测试代码
    model = load_animal_model()
    print("Model loaded successfully") 