#!/usr/bin/env python3

# Read the file
with open('animal_pose_matcher.py', 'r') as f:
    lines = f.readlines()

# Find the line with "overlayCtx.moveTo(x, y)" and replace it
for i in range(len(lines)):
    if "overlayCtx.moveTo(x, y)" in lines[i]:
        lines[i] = lines[i].replace("overlayCtx.moveTo(x, y)", "overlayCtx.moveTo(finalX, finalY)")
    if "overlayCtx.lineTo(x, y)" in lines[i]:
        lines[i] = lines[i].replace("overlayCtx.lineTo(x, y)", "overlayCtx.lineTo(finalX, finalY)")

# Write the modified content back to the file
with open('animal_pose_matcher.py', 'w') as f:
    f.writelines(lines)

print("File updated successfully!")
