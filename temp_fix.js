// Original code
                                for (let i = 0; i < contour.length; i++) {
                                    const point = contour[i][0];
                                    const x = point[0] * scaleX; 
                                    const y = point[1] * scaleY;
                                    
                                    if (i === 0) {
                                        overlayCtx.moveTo(x, y);
                                    } else {
                                        overlayCtx.lineTo(x, y);
                                    }
                                }

// Modified code
                                for (let i = 0; i < contour.length; i++) {
                                    const point = contour[i][0];
                                    const x = point[0] * scaleX;
                                    const y = point[1] * scaleY;
                                    
                                    // Get video element position offset
                                    const videoOffsetLeft = video.offsetLeft;
                                    const videoOffsetTop = video.offsetTop;
                                    
                                    // Add video position offset to coordinates
                                    const finalX = x + videoOffsetLeft;
                                    const finalY = y + videoOffsetTop;
                                    
                                    if (i === 0) {
                                        overlayCtx.moveTo(finalX, finalY);
                                    } else {
                                        overlayCtx.lineTo(finalX, finalY);
                                    }
                                }
