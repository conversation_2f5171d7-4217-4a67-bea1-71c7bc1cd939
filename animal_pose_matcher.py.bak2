#!/usr/bin/env python3
"""
Animal Pose Matcher

This script captures an image using your webcam, extracts human contours,
and finds the most similar animal pose from a database.

Usage:
    python3 animal_pose_matcher.py

Controls:
    - Press SPACE to take a photo
    - Press 'q' to quit
"""

import os
import cv2
import time
import uuid
import json
import pickle
import base64
import pathlib
import webbrowser
import traceback
import threading
import http.server
import socketserver
import urllib.parse
import matplotlib.pyplot as plt
import numpy as np
from pathlib import Path
from scipy.spatial import KDTree
from ultralytics import YOLO

# 添加OpenAI API支持
import requests
from urllib.parse import parse_qs
import random
import hashlib
import google.generativeai as genai  # 新增: 导入Google Gemini API库


class ContourRetrieval:
    """Contour Retrieval System"""

    def __init__(self, base_dir=None, results_dir=None, human_model_path=None, animal_db_path=None):
        """Initialize retrieval system"""
        # If no path is provided, use default path
        if base_dir is None:
            self.base_dir = Path('/Users/<USER>/Desktop/pose_matching_project')
        else:
            self.base_dir = Path(base_dir)

        if results_dir is None:
            self.results_dir = self.base_dir / 'results'
        else:
            self.results_dir = Path(results_dir)

        if human_model_path is None:
            self.human_model_path = self.base_dir / 'models' / 'human_seg_model.pt'
        else:
            self.human_model_path = Path(human_model_path)

        if animal_db_path is None:
            self.animal_db_path = self.base_dir / 'database' / 'animal_contours.pkl'
        else:
            self.animal_db_path = Path(animal_db_path)

        # Create results directory (if it doesn't exist)
        self.results_dir.mkdir(parents=True, exist_ok=True)

        # Load human segmentation model
        print("Loading human segmentation model...")
        try:
            self.human_model = YOLO(str(self.human_model_path))
        except Exception as e:
            print(f"Unable to load human segmentation model: {e}")
            raise

        # Load animal contour database
        print("Loading animal contour database...")
        try:
            with open(self.animal_db_path, 'rb') as f:
                self.animal_db = pickle.load(f)
            self.build_index()
        except Exception as e:
            print(f"Unable to load animal contour database: {e}")
            raise

        # Load animal recognition model (YOLOv8)
        print("Loading animal recognition model...")
        try:
            # 使用YOLOv8预训练模型进行动物识别
            self.animal_recognition_model = YOLO('yolov8n.pt')  # 使用YOLOv8通用检测模型
        except Exception as e:
            print(f"Unable to load animal recognition model: {e}")
            self.animal_recognition_model = None

        print("Initialization complete!")

    def build_index(self):
        """Build KD-tree index for fast retrieval"""
        # Calculate features for each contour
        self.features = []
        for contour in self.animal_db['contours']:
            features = self.compute_shape_features(contour)
            self.features.append(features)

        # Convert feature list to numpy array
        self.features = np.array(self.features)

        # Handle infinite values and NaN values
        self.features = np.nan_to_num(self.features, nan=0.0, posinf=0.0, neginf=0.0)

        # Build KD-tree
        self.kdtree = KDTree(self.features)

    def compute_shape_features(self, contour):
        """Calculate shape features for contour"""
        # Calculate Hu moments
        moments = cv2.moments(contour)
        if moments['m00'] != 0:
            hu_moments = cv2.HuMoments(moments).flatten()
        else:
            hu_moments = np.zeros(7)

        # Safe logarithmic transformation
        log_hu = np.zeros_like(hu_moments)
        for i, hu in enumerate(hu_moments):
            if abs(hu) > 1e-7:  # Avoid taking log of zero
                log_hu[i] = -np.sign(hu) * np.log10(abs(hu))
            else:
                log_hu[i] = 0

        # Calculate area and perimeter ratio
        area = cv2.contourArea(contour)
        perimeter = cv2.arcLength(contour, True)
        if perimeter > 0:
            circularity = 4 * np.pi * area / (perimeter * perimeter)
        else:
            circularity = 0

        # Calculate contour orientation
        if len(contour) >= 5:  # Need at least 5 points to fit ellipse
            try:
                (x, y), (ma, mi), angle = cv2.fitEllipse(contour)
                aspect_ratio = ma / mi if mi > 0 else 1.0
            except:
                aspect_ratio = 1.0
                angle = 0
        else:
            aspect_ratio = 1.0
            angle = 0

        # Combine features
        features = np.concatenate([
            log_hu,  # Hu moments
            [circularity],  # Circularity
            [aspect_ratio],  # Aspect ratio
            [np.sin(angle * np.pi / 180), np.cos(angle * np.pi / 180)]  # Direction (sine and cosine)
        ])

        # Handle infinite values and NaN values
        features = np.nan_to_num(features, nan=0.0, posinf=0.0, neginf=0.0)

        return features

    def extract_human_contour(self, image_path):
        """Extract contour from human image"""
        # Use YOLO model for prediction
        results = self.human_model(image_path, verbose=False)

        # Get segmentation masks
        if not results or not results[0].masks:
            raise ValueError("No human contour detected")

        # Get human masks (select the human detection result with highest confidence)
        person_masks = []
        for r in results[0].boxes.data:
            if int(r[5]) == 0:  # 0 represents person in COCO dataset
                mask_idx = int(r[4])  # Get corresponding mask index
                if mask_idx < len(results[0].masks):
                    person_masks.append((float(r[4]), results[0].masks[mask_idx].data[0].cpu().numpy()))

        if not person_masks:
            raise ValueError("No human contour detected")

        # Use the human mask with highest confidence
        mask = max(person_masks, key=lambda x: x[0])[1]

        # Extract contour
        mask = (mask * 255).astype(np.uint8)
        contours, _ = cv2.findContours(mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

        if not contours:
            raise ValueError("No human contour detected")

        # Get the largest contour
        max_contour = max(contours, key=cv2.contourArea)

        # Normalize contour
        x, y, w, h = cv2.boundingRect(max_contour)
        max_contour = max_contour - [x, y]
        scale = 100.0 / max(w, h)
        max_contour = (max_contour * scale).astype(np.float32)

        return max_contour

    def retrieve_similar_poses(self, image_path, top_k=1):
        """Retrieve similar poses from database"""
        # Extract human contour from input image
        human_contour = self.extract_human_contour(image_path)

        if human_contour is None:
            print("No human contour detected in image")
            return []

        # Retrieve similar contours
        results = self.retrieve(human_contour, top_k=top_k)

        # Visualize results
        html_path = self.visualize_results(image_path, human_contour, results)

        return results

    def create_html_result(self, image_path, human_contour, results):
        """Create HTML-formatted result page"""
        if not results:
            return None

        # Get result data
        result = results[0]  # Use only the most matching result
        animal_category = result['category']
        similarity_score = int(result['similarity_score'] * 100)

        # Prepare image paths
        human_img_filename = "human_pose.jpg"
        animal_img_filename = "animal_pose.jpg"
        human_img_path = str(self.results_dir / human_img_filename)
        animal_img_path = str(self.results_dir / animal_img_filename)

        # Save human image (with contour)
        query_img = cv2.imread(str(image_path))
        query_img = cv2.cvtColor(query_img, cv2.COLOR_BGR2RGB)

        # Create human image contour overlay
        overlay_img = query_img.copy()
        img_h, img_w = query_img.shape[:2]
        resize_factor = min(img_h, img_w) / 100.0
        resized_contour = (human_contour * resize_factor).astype(np.int32)

        # 修复人物轮廓定位 - 计算完整边界框然后居中
        x, y, w, h = cv2.boundingRect(resized_contour)
        center_offset_x = (img_w - w) // 2 - x
        center_offset_y = (img_h - h) // 2 - y

        centered_contour = resized_contour + [center_offset_x, center_offset_y]
        cv2.drawContours(overlay_img, [centered_contour], -1, (0, 255, 0), 2)

        # Save human image
        plt.imsave(human_img_path, overlay_img)

        # Save animal image (with contour)
        # img_path = self.base_dir / 'database' / 'images' / f"{result['image_id']}.jpg"
        # 使用museum文件夹中的图片
        museum_dir = Path('/Users/<USER>/Desktop/museum')
        # 使用图像ID作为索引选择museum文件夹中的图片
        museum_images = sorted(list(museum_dir.glob('*.JPG')))
        idx = int(result['image_id']) % len(museum_images)
        img_path = museum_images[idx]
        animal_img = cv2.imread(str(img_path))
        animal_img = cv2.cvtColor(animal_img, cv2.COLOR_BGR2RGB)

        # Create animal image contour overlay
        animal_overlay = animal_img.copy()
        animal_h, animal_w = animal_img.shape[:2]
        animal_resize_factor = min(animal_h, animal_w) / 100.0
        animal_contour = result['contour']
        resized_animal_contour = (animal_contour * animal_resize_factor).astype(np.int32)

        # 修复动物轮廓定位 - 计算完整边界框然后居中
        x, y, w, h = cv2.boundingRect(resized_animal_contour)
        animal_offset_x = (animal_w - w) // 2 - x
        animal_offset_y = (animal_h - h) // 2 - y

        centered_animal_contour = resized_animal_contour + [animal_offset_x, animal_offset_y]
        cv2.drawContours(animal_overlay, [centered_animal_contour], -1, (0, 255, 0), 2)

        # Save animal image
        plt.imsave(animal_img_path, animal_overlay)

        # 为动物获取博物馆位置信息
        museum_location = "Asian Mammals, Floor 2, East Wing"  # 默认位置

        # Create HTML content
        html_content = f"""<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Animal Pose Matcher Result</title>
    <style>
        * {{
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }}

        body {{
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
            margin: 0;
            padding: 0;
            width: 100vw;
            height: 100vh;
            overflow: hidden;
            background-color: #000;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
        }}

        /* 全屏布局 */
        .container {{
            position: relative;
            width: 100%;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
        }}

        /* 结果区域 - 使用Flexbox进行居中布局 */
        .result {{
            display: flex;
            justify-content: space-between;
            align-items: center;
            gap: 50px;
        }}

        .result-column {{
            text-align: center;
            position: relative;
        }}

        .match-info {{
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            padding: 0 30px;
        }}

        .match-score {{
            width: 100px;
            height: 100px;
            line-height: 100px;
            background-color: rgba(0, 0, 0, 0.7);
            color: white;
            border-radius: 50%;
            margin: 0 auto;
            font-size: 24px;
            font-weight: bold;
        }}

        img {{
            max-width: 100%;
            max-height: 100%;
            object-fit: contain;
        }}

        .side {{
            position: relative;
            border: 1px solid #333;
            border-radius: 10px;
            overflow: hidden;
            width: 400px;
            height: 400px;
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: #000; /* 黑色背景 */
        }}

        .info-btn {{
            position: absolute;
            bottom: 80px;  /* 上移，为location-btn留出空间 */
            left: 16px;
            background-color: rgba(0, 0, 0, 0.7);
            color: white;
            padding: 18px;
            border-radius: 12px;
            backdrop-filter: blur(4px);
            border: 2px solid rgba(255, 255, 255, 0.2);
            cursor: pointer;
            width: 280px;
            display: flex;
            flex-direction: column;
            align-items: flex-start;
            gap: 12px;
            font-weight: 500;
            transition: all 0.2s;
            box-shadow: 0 8px 15px -1px rgba(0, 0, 0, 0.3);
        }}

        .info-btn:hover {{
            background-color: rgba(0, 0, 0, 0.9);
            border-color: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }}

        .animal-title {{
            font-size: 20px;
            font-weight: 600;
            margin: 0;
        }}

        .info-btn-description {{
            font-size: 14px;
            opacity: 0.8;
            margin: 0;
            line-height: 1.5;
        }}

        .location-link {{
            display: flex;
            align-items: center;
            gap: 6px;
            font-size: 12px;
            color: #4dabf7;
            margin-top: 4px;
        }}

        .v0-block {{
            position: absolute;
            top: 0;
            left: 0;
            background-color: rgba(0,0,0,0.5);
            color: white;
            padding: 5px;
            font-size: 12px;
            z-index: 5;
        }}

        .control-btn {{
            background-color: rgba(0, 0, 0, 0.7);
            color: white;
            border: 1px solid #333;
            padding: 12px 24px;
            border-radius: 6px;
            font-size: 16px;
            cursor: pointer;
            margin: 10px;
            transition: background-color 0.2s;
        }}

        .control-btn:hover {{
            background-color: rgba(40, 40, 40, 0.7);
        }}

        /* 动物信息模态框 */
        #animalInfoModal {{
            display: none;
            position: fixed;
            inset: 0;
            background-color: rgba(0, 0, 0, 0.7);
            backdrop-filter: blur(8px);
            z-index: 1000;
            align-items: center;
            justify-content: center;
            padding: 20px;
            overflow-y: auto;
        }}

        .modal-content {{
            background-color: white;
            border-radius: 12px;
            max-width: 700px;
            width: 100%;
            padding: 20px;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.5);
            color: #333;
            overflow-y: auto;
            max-height: 90vh;
        }}

        .modal-header {{
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }}

        .modal-title {{
            font-size: 32px;
            font-weight: bold;
            display: flex;
            align-items: center;
            gap: 10px;
            color: #000;
        }}

        .status-tag {{
            background-color: #D97706;
            color: white;
            padding: 4px 12px;
            border-radius: 9999px;
            font-size: 14px;
            font-weight: normal;
        }}

        .close-btn {{
            background: none;
            border: none;
            color: #888;
            font-size: 32px;
            cursor: pointer;
        }}

        .modal-body {{
            color: #333;
            line-height: 1.6;
        }}

        .animal-description {{
            margin-bottom: 15px;
        }}

        .museum-location {{
            font-style: italic;
            color: #aaa;
            margin-top: 10px;
        }}
    </style>
</head>
<body>
    <div class="container">
        <div class="result">
            <div class="result-column">
                <div class="side">
                    <img src="/images/{animal_img_filename}" alt="{animal_category} with segmentation">
                    <div class="v0-block">v0 block</div>
                    <!-- 动物信息按钮 -->
                    <div class="info-btn" onclick="showAnimalInfo()">
                        <h3 class="animal-title">{animal_category}</h3>
                        <p class="info-btn-description">XXXXXXXXXXXXXXXXXXXXX XXXXXXXXXXXXXXXX</p>
                        <div class="location-link" onclick="showMuseumMap(event)">
                            <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="#4dabf7" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <path d="M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z"></path>
                                <circle cx="12" cy="10" r="3"></circle>
                            </svg>
                            <span>查看博物馆位置</span>
                        </div>
                    </div>
                </div>
            </div>

            <div class="match-info">
                <div class="match-score">{similarity_score}%</div>
                <p>Match Score</p>

                <!-- Buttons for saving results -->
                <button class="control-btn" onclick="saveResult()" style="margin-top: 20px; width: 180px;">Save Result</button>
                <button class="control-btn" onclick="findInMuseum()" style="margin-top: 10px; width: 180px;">Find in Museum</button>
            </div>

            <div class="result-column">
                <div class="side">
                    <img src="/images/{human_img_filename}" alt="Your Pose">
                </div>
            </div>
        </div>
    </div>

    <!-- 动物信息模态框 -->
    <div id="animalInfoModal">
        <div class="modal-content">
            <div class="modal-header">
                <h2 class="modal-title">
                    {animal_category}
                    <span class="status-tag">Vulnerable</span>
                </h2>
                <button class="close-btn" onclick="hideAnimalInfo()">&times;</button>
            </div>
            <div class="modal-body">
                <h3 class="section-title">Photos</h3>
                <div class="unsplash-photos">
                    <p class="loading-photos">Loading photos from Unsplash...</p>
                    <!-- Photos from Unsplash will be loaded here -->
                </div>

                <h3 class="section-title">About</h3>
                <p class="animal-description">
                    This magnificent animal is part of our museum collection. Visit the exhibit to learn more about its habitat, behavior, and conservation status.
                </p>

                <div class="info-grid">
                    <div class="info-section">
                        <h3 class="section-title">Diet</h3>
                        <p>Varies by species</p>
                    </div>

                    <div class="info-section">
                        <h3 class="section-title">Habitat</h3>
                        <p>Natural habitats across Africa</p>
                    </div>

                    <div class="info-section">
                        <h3 class="section-title">Lifespan</h3>
                        <p>15-30 years in the wild</p>
                    </div>

                    <div class="info-section">
                        <h3 class="section-title">Weight</h3>
                        <p>Varies by species and age</p>
                    </div>
                </div>

                <div class="fun-fact">
                    <div class="fun-fact-title">Fun Fact</div>
                    <p>This animal is part of a critical conservation effort at our museum.</p>
                </div>

                <h3 class="section-title">Museum Location</h3>
                <div class="location-card">
                    <div class="location-header">
                        <div class="location-icon">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <path d="M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z"></path>
                                <circle cx="12" cy="10" r="3"></circle>
                            </svg>
                        </div>
                        <span>Main Exhibition Hall</span>
                    </div>
                    <p>Follow the signs or ask a museum guide for directions to this exhibit.</p>
                </div>

                <div class="map-placeholder">
                    Museum Map Placeholder
                </div>

                <button class="close-button" onclick="hideAnimalInfo()">Close</button>
            </div>
        </div>
    </div>

    <script>
        function saveResult() {{
            alert('Result saved!');
        }}

        function findInMuseum() {{
            alert('Locating {animal_category} in the museum: African Mammals, Floor 1, South Wing');
        }}

        function showAnimalInfo() {{
            // 显示加载状态
            const animalTitle = document.querySelector('.modal-title');
            if (animalTitle) {{
                animalTitle.innerHTML = '{animal_category} <span class="status-tag">Loading...</span>';
            }}

            // 显示模态框，但先展示加载中状态
            document.getElementById('animalInfoModal').style.display = 'flex';

            // 所有可能需要更新的元素
            const animalDescription = document.querySelector('.animal-description');
            const statusTag = document.querySelector('.status-tag');
            const dietSection = document.querySelector('.info-grid .info-section:nth-child(1) p');
            const habitatSection = document.querySelector('.info-grid .info-section:nth-child(2) p');
            const lifespanSection = document.querySelector('.info-grid .info-section:nth-child(3) p');
            const weightSection = document.querySelector('.info-grid .info-section:nth-child(4) p');
            const funFactText = document.querySelector('.fun-fact p');
            const locationSpan = document.querySelector('.location-header span');

            // 初始设置为加载中
            if (animalDescription) animalDescription.textContent = 'Loading...';
            if (dietSection) dietSection.textContent = 'Loading...';
            if (habitatSection) habitatSection.textContent = 'Loading...';
            if (lifespanSection) lifespanSection.textContent = 'Loading...';
            if (weightSection) weightSection.textContent = 'Loading...';
            if (funFactText) funFactText.textContent = 'Loading...';

            // 调用API获取动物信息
            fetch(`/api/animal-info?name={animal_category}`)
                .then(response => response.json())
                .then(result => {{
                    if (result.success && result.data) {{
                        const data = result.data;

                        // 更新标题和状态
                        if (animalTitle) {{
                            animalTitle.innerHTML = `{animal_category} <span class="status-tag">${{data.conservation_status || 'Unknown'}}</span>`;
                        }}

                        // 更新各部分内容
                        if (animalDescription) animalDescription.textContent = data.description || 'No description available.';
                        if (dietSection) dietSection.textContent = data.diet || 'Information not available';
                        if (habitatSection) habitatSection.textContent = data.habitat || 'Information not available';
                        if (lifespanSection) lifespanSection.textContent = data.lifespan || 'Information not available';
                        if (weightSection) weightSection.textContent = data.weight || 'Information not available';
                        if (funFactText) funFactText.textContent = data.fun_fact || 'No fun fact available.';

                        // 更新展览位置 - 使用地理分布
                        if (locationSpan) {{
                            locationSpan.textContent = data.geographic_range || 'Main Exhibition Hall';
                        }}

                        // 加载Unsplash图片
                        loadUnsplashPhotos('{animal_category}');

                        document.getElementById('animalInfoModal').style.display = 'flex';
                    }} else {{
                        // 如果API调用失败，显示错误并使用备用数据
                        console.error('Failed to get animal info:', result.error);

                        // 回退到默认信息
                        if (animalTitle) {{
                            animalTitle.innerHTML = '{animal_category} <span class="status-tag">Unknown</span>';
                        }}

                        if (animalDescription) {{
                            animalDescription.textContent = 'This magnificent animal is part of our museum collection. Visit the exhibit to learn more about its habitat, behavior, and conservation status.';
                        }}

                        if (dietSection) dietSection.textContent = 'Varies by species';
                        if (habitatSection) habitatSection.textContent = 'Natural habitats';
                        if (lifespanSection) lifespanSection.textContent = 'Varies by species';
                        if (weightSection) weightSection.textContent = 'Varies by species and age';
                        if (funFactText) funFactText.textContent = 'This animal is part of a critical conservation effort at our museum.';
                        if (locationSpan) locationSpan.textContent = 'Main Exhibition Hall';

                        // 显示模态框
                        document.getElementById('animalInfoModal').style.display = 'flex';
                    }}
                }})
                .catch(error => {{
                    // 处理网络错误
                    console.error('Error fetching animal info:', error);

                    // 回退到默认信息
                    if (animalTitle) {{
                        animalTitle.innerHTML = '{animal_category} <span class="status-tag">Unknown</span>';
                    }}

                    if (animalDescription) {{
                        animalDescription.textContent = 'This magnificent animal is part of our museum collection. Information is currently unavailable.';
                    }}

                    if (statusTag) statusTag.textContent = 'Unknown';
                    if (dietSection) dietSection.textContent = 'Information unavailable';
                    if (habitatSection) habitatSection.textContent = 'Information unavailable';
                    if (lifespanSection) lifespanSection.textContent = 'Information unavailable';
                    if (weightSection) weightSection.textContent = 'Information unavailable';
                    if (funFactText) funFactText.textContent = 'Information is currently unavailable.';
                    if (locationSpan) locationSpan.textContent = 'Main Exhibition Hall';

                    // 显示模态框
                    document.getElementById('animalInfoModal').style.display = 'flex';
                }});
        }}

        // 备用函数，当API请求失败时使用
        function setFallbackAnimalInfo() {{
            // 设置动物信息
            const animalTitle = document.querySelector('.modal-title');
            if (animalTitle) {{
                animalTitle.innerHTML = '{animal_category} <span class="status-tag">Vulnerable</span>';
            }}

            // 更新动物描述（备用数据）
            const animalDescriptions = {{
                'Giraffe': 'The giraffe is the tallest living terrestrial animal. Its distinctive features are its extremely long neck and legs, horn-like ossicones, and spotted coat patterns. Giraffes have specialized cardiovascular systems to manage blood pressure between their brain and heart.',
                'Elephant': 'Elephants are the largest existing land animals and are known for their intelligence, long trunks, and tusks. They have complex social structures and are highly intelligent animals with excellent memory.',
                'Lion': 'Lions are large cats belonging to the genus Panthera. They have muscular, deep-chested bodies and short, rounded heads. The male lion is distinguished by his mane, which is absent in female lions.',
                'Zebra': 'Zebras are African equines with distinctive black-and-white striped coats. Each zebra has its own unique pattern of stripes, as distinctive as human fingerprints.',
                'Rhino': 'Rhinoceroses are large, herbivorous mammals identified by their characteristic horned snouts. Their thick protective skin is formed from layers of collagen.'
            }};

            const animalDescription = document.querySelector('.animal-description');
            if (animalDescription) {{
                animalDescription.textContent = animalDescriptions['{animal_category}'] ||
                    'This magnificent animal is part of our museum collection. Visit the exhibit to learn more about its habitat, behavior, and conservation status.';
            }}

            // 设置信息卡片内容（备用数据）
            const dietInfo = {{
                'Giraffe': 'Herbivore - primarily acacia leaves and shoots',
                'Elephant': 'Herbivore - up to 300kg of vegetation daily',
                'Lion': 'Carnivore - primarily large ungulates',
                'Zebra': 'Herbivore - mainly grasses',
                'Rhino': 'Herbivore - grasses, leaves, fruits'
            }};

            const habitatInfo = {{
                'Giraffe': 'Savannas, grasslands, and open woodlands',
                'Elephant': 'Forests, deserts, marshes, and savannas',
                'Lion': 'Grasslands, savannas, and open woodlands',
                'Zebra': 'Plains, grasslands, and light woodlands',
                'Rhino': 'Grasslands and floodplains'
            }};

            const lifespanInfo = {{
                'Giraffe': '20-25 years in the wild',
                'Elephant': '60-70 years',
                'Lion': '10-14 years in the wild',
                'Zebra': '25-30 years in the wild',
                'Rhino': '35-50 years'
            }};

            const weightInfo = {{
                'Giraffe': '800-1,930 kg (1,760-4,250 lb)',
                'Elephant': '2,700-6,000 kg (6,000-13,000 lb)',
                'Lion': '150-250 kg (330-550 lb)',
                'Zebra': '350-450 kg (770-990 lb)',
                'Rhino': '1,800-2,700 kg (4,000-6,000 lb)'
            }};

            const dietSection = document.querySelector('.info-grid .info-section:nth-child(1) p');
            const habitatSection = document.querySelector('.info-grid .info-section:nth-child(2) p');
            const lifespanSection = document.querySelector('.info-grid .info-section:nth-child(3) p');
            const weightSection = document.querySelector('.info-grid .info-section:nth-child(4) p');

            if (dietSection) dietSection.textContent = dietInfo['{animal_category}'] || 'Varies by species';
            if (habitatSection) habitatSection.textContent = habitatInfo['{animal_category}'] || 'Natural habitats across Africa';
            if (lifespanSection) lifespanSection.textContent = lifespanInfo['{animal_category}'] || '15-30 years in the wild';
            if (weightSection) weightSection.textContent = weightInfo['{animal_category}'] || 'Varies by species and age';

            // 设置有趣的事实（备用数据）
            const funFacts = {{
                'Giraffe': 'Giraffes only need 5-30 minutes of sleep in a 24-hour period, often taken in very short naps.',
                'Elephant': 'Elephants can recognize themselves in mirrors, showing self-awareness that few animals possess.',
                'Lion': 'Lions can roar so loudly it can be heard up to 8 kilometers away.',
                'Zebra': 'Each zebra has a unique stripe pattern, like human fingerprints.',
                'Rhino': 'Rhinos are known to have poor eyesight but excellent hearing and sense of smell.'
            }};

            const funFactText = document.querySelector('.fun-fact p');
            if (funFactText) {{
                funFactText.textContent = funFacts['{animal_category}'] ||
                    'This animal is part of a critical conservation effort at our museum.';
            }}
        }}

        function hideAnimalInfo() {{
            document.getElementById('animalInfoModal').style.display = 'none';
        }}

        function showMuseumMap(event) {{
            // 阻止事件冒泡，这样点击location-link不会触发父元素info-btn的点击事件
            if (event) {{
                event.stopPropagation();
            }}
            alert('Museum map for {animal_category} location');
        }}

        function loadUnsplashPhotos(animalName) {{
            const photosContainer = document.querySelector('.unsplash-photos');
            if (!photosContainer) return;

            // 显示加载中状态
            photosContainer.innerHTML = '<p style="text-align:center; padding:20px; color:#666; font-style:italic;">Loading photos from Unsplash...</p>';

            // 从API获取Unsplash图片
            fetch(`/api/unsplash-images?name=${{animalName}}`)
                .then(response => response.json())
                .then(data => {{
                    if (data.success && data.images && data.images.length > 0) {{
                        // 清除加载消息
                        photosContainer.innerHTML = '';

                        // 创建水平滚动容器
                        const photoGrid = document.createElement('div');
                        photoGrid.style.display = 'flex';
                        photoGrid.style.overflowX = 'auto';
                        photoGrid.style.scrollBehavior = 'smooth';
                        photoGrid.style.padding = '10px 0';
                        photoGrid.style.gap = '15px';
                        photoGrid.style.marginBottom = '15px';

                        // 添加每张图片
                        data.images.forEach(image => {{
                            const photoItem = document.createElement('div');
                            photoItem.style.flex = '0 0 auto';
                            photoItem.style.width = '250px';
                            photoItem.style.borderRadius = '8px';
                            photoItem.style.overflow = 'hidden';

                            const img = document.createElement('img');
                            img.src = image.url;
                            img.alt = `Photo of ${{animalName}}`;
                            img.style.width = '100%';
                            img.style.height = '180px';
                            img.style.objectFit = 'cover';
                            img.style.borderRadius = '8px';
                            img.style.transition = 'transform 0.3s';

                            // 鼠标悬停效果
                            img.onmouseover = function() {{ this.style.transform = 'scale(1.03)'; }};
                            img.onmouseout = function() {{ this.style.transform = 'scale(1)'; }};

                            const attribution = document.createElement('div');
                            attribution.style.fontSize = '12px';
                            attribution.style.padding = '8px 0';
                            attribution.style.textAlign = 'center';
                            attribution.style.color = '#666';
                            attribution.innerHTML = `Photo by <a href="${{image.photographer_url}}" style="color:#333; text-decoration:none;" target="_blank" rel="noopener noreferrer">${{image.photographer}}</a> on <a href="https://unsplash.com/?utm_source=animal_pose_matcher&utm_medium=referral" style="color:#333; text-decoration:none;" target="_blank" rel="noopener noreferrer">Unsplash</a>`;

                            photoItem.appendChild(img);
                            photoItem.appendChild(attribution);
                            photoGrid.appendChild(photoItem);
                        }});

                        // 添加简单的滚动提示
                        const scrollHint = document.createElement('div');
                        scrollHint.style.textAlign = 'center';
                        scrollHint.style.fontSize = '12px';
                        scrollHint.style.color = '#666';
                        scrollHint.style.marginTop = '5px';
                        scrollHint.innerHTML = '← Swipe left or right to view more photos →';

                        photosContainer.appendChild(photoGrid);
                        photosContainer.appendChild(scrollHint);
                    }} else {{
                        photosContainer.innerHTML = '<p style="text-align:center; padding:20px; color:#666; font-style:italic;">No photos found.</p>';
                    }}
                }})
                .catch(error => {{
                    console.error('Error loading Unsplash photos:', error);
                    photosContainer.innerHTML = '<p style="text-align:center; padding:20px; color:#666; font-style:italic;">Failed to load photos</p>';
                }});
        }}
    </script>
</body>
</html>"""

        # Save HTML file
        html_path = str(self.results_dir / "result.html")
        with open(html_path, 'w', encoding='utf-8') as f:
            f.write(html_content)

        return html_path

    def visualize_results(self, image_path, human_contour, results):
        """Visualize retrieval results"""
        # Create regular matplotlib visualization
        n_results = len(results)

        # For single result case, make figures larger
        if n_results == 1:
            fig, axes = plt.subplots(1, 2, figsize=(12, 6))
            fig.suptitle('Animal Pose Matching Result', fontsize=16, y=0.98)
        else:
            fig, axes = plt.subplots(1, n_results + 1, figsize=(4*(n_results + 1), 5))

        fig.patch.set_facecolor('white')

        # Display input image with contour overlay
        query_img = cv2.imread(str(image_path))
        query_img = cv2.cvtColor(query_img, cv2.COLOR_BGR2RGB)

        # 截取摄像头图像中央1:1区域
        h, w = query_img.shape[:2]
        if w > h:
            # 宽大于高，裁剪宽度
            crop_size = h
            start_x = (w - crop_size) // 2
            cropped_img = query_img[:, start_x:start_x+crop_size]
        else:
            # 高大于宽，裁剪高度
            crop_size = w
            start_y = (h - crop_size) // 2
            cropped_img = query_img[start_y:start_y+crop_size, :]

        # Create a copy of input image for overlaying contour
        overlay_img = cropped_img.copy()

        # Resize human contour to match input image dimensions
        img_h, img_w = overlay_img.shape[:2]
        resize_factor = min(img_h, img_w) / 100.0  # Assuming contour was normalized to 100x100

        # Adjust contour to center of image
        resized_contour = (human_contour * resize_factor).astype(np.int32)
        center_offset_x = (img_w - resized_contour[:, 0, 0].max()) // 2
        center_offset_y = (img_h - resized_contour[:, 0, 1].max()) // 2
        centered_contour = resized_contour + [center_offset_x, center_offset_y]

        # Draw human contour on original image
        cv2.drawContours(overlay_img, [centered_contour], -1, (0, 255, 0), 2)

        # Access first index properly for both single result and multiple results
        if n_results == 1:
            ax_human = axes[0]
        else:
            ax_human = axes[0]

        # Show overlay image
        ax_human.imshow(overlay_img)
        ax_human.axis('off')
        ax_human.set_title('Your Pose', fontsize=14, pad=10)

        # 获取label museum中的所有JSON文件
        label_museum_dir = Path('/Users/<USER>/Desktop/label museum')
        json_files = list(label_museum_dir.glob('*.json'))

        # 将图片ID映射到对应的JSON文件
        json_map = {os.path.splitext(os.path.basename(str(f)))[0]: f for f in json_files}

        # 获取museum文件夹中对应的图片
        museum_dir = Path('/Users/<USER>/Desktop/museum')

        # Display retrieval results
        for i, result in enumerate(results):
            # 从label museum中的JSON文件查找有效的图片
            valid_images = []
            for img_file in museum_dir.glob('*.JPG'):
                img_name = os.path.splitext(os.path.basename(str(img_file)))[0]
                if img_name in json_map:
                    valid_images.append(img_file)

            if not valid_images:
                print("没有找到与JSON标注对应的图片")
                continue

            # 选择一个有效的图片
            idx = int(result['image_id']) % len(valid_images)
            img_path = valid_images[idx]
            img_name = os.path.splitext(os.path.basename(str(img_path)))[0]

            # 识别图片中的动物（使用JSON标注）
            animal_type, animal_mask = self.detect_animal_in_image(str(img_path))

            # 注意：不再覆盖retrieve方法确定的结果，确保动物类别完全由几何轮廓匹配决定
            # 原代码: result['category'] = animal_type (已删除)

            # 记录检测到的动物类型，但不影响匹配结果
            detected_animal = animal_type  # 只用于日志记录
            print(f"Retrieved result category: {result['category']}, Detected animal: {detected_animal}")

            try:
                animal_img = cv2.imread(str(img_path))
                if animal_img is None:
                    print(f"Could not read image: {img_path}")
                    continue

                animal_img = cv2.cvtColor(animal_img, cv2.COLOR_BGR2RGB)

                # 创建1:1比例的黑色背景
                animal_h, animal_w = animal_img.shape[:2]
                square_size = max(animal_h, animal_w)
                square_img = np.zeros((square_size, square_size, 3), dtype=np.uint8)

                # 计算缩放比例
                if animal_h > animal_w:
                    # 高大于宽，按高度缩放
                    scale = square_size / animal_h
                    new_width = int(animal_w * scale)
                    resized_animal = cv2.resize(animal_img, (new_width, square_size))
                    # 计算水平居中的位置
                    start_x = (square_size - new_width) // 2
                    # 将调整大小的图像放置在黑色背景上
                    square_img[:, start_x:start_x+new_width] = resized_animal
                else:
                    # 宽大于高，按宽度缩放
                    scale = square_size / animal_w
                    new_height = int(animal_h * scale)
                    resized_animal = cv2.resize(animal_img, (square_size, new_height))
                    # 计算垂直居中的位置
                    start_y = (square_size - new_height) // 2
                    # 将调整大小的图像放置在黑色背景上
                    square_img[start_y:start_y+new_height, :] = resized_animal

                # Create copy for contour overlay
                animal_overlay = square_img.copy()

                # 如果有动物掩码，使用它绘制轮廓
                if animal_mask is not None:
                    # 调整掩码大小以匹配调整后的图像
                    if animal_h > animal_w:
                        # 高大于宽情况
                        resized_mask = cv2.resize(animal_mask, (new_width, square_size))
                        mask_square = np.zeros((square_size, square_size), dtype=np.uint8)
                        mask_square[:, start_x:start_x+new_width] = resized_mask
                    else:
                        # 宽大于高情况
                        resized_mask = cv2.resize(animal_mask, (square_size, new_height))
                        mask_square = np.zeros((square_size, square_size), dtype=np.uint8)
                        mask_square[start_y:start_y+new_height, :] = resized_mask

                    # 提取轮廓
                    contours, _ = cv2.findContours(mask_square, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
                    if contours:
                        # 绘制最大的轮廓
                        max_contour = max(contours, key=cv2.contourArea)
                        cv2.drawContours(animal_overlay, [max_contour], -1, (0, 255, 0), 2)
                else:
                    # 使用原始的动物轮廓
                    animal_contour = result['contour']
                    # 调整轮廓大小并定位到正方形图像中
                    if animal_h > animal_w:
                        # 高大于宽
                        resized_contour = (animal_contour * scale).astype(np.int32)
                        # 水平偏移
                        resized_contour = resized_contour + [start_x, 0]
                    else:
                        # 宽大于高
                        resized_contour = (animal_contour * scale).astype(np.int32)
                        # 垂直偏移
                        resized_contour = resized_contour + [0, start_y]

                    cv2.drawContours(animal_overlay, [resized_contour], -1, (0, 255, 0), 2)

                # Access axis properly for both cases
                if n_results == 1:
                    ax_animal = axes[1]
                else:
                    ax_animal = axes[i+1]

                # Show overlay
                ax_animal.imshow(animal_overlay)
                ax_animal.axis('off')

                # Use better title formatting for single result
                if n_results == 1:
                    title = f"{result['category']}\n{result['similarity_score']:.0f}% Match"
                    ax_animal.set_title(title, fontsize=14, pad=10)
                else:
                    ax_animal.set_title(f"{result['category']}\n{result['similarity_score']:.2f}",
                                      fontsize=12, pad=10)

            except Exception as e:
                print(f"Error displaying result {i+1}: {e}")

        # Add similarity score between the images for single result case
        if n_results == 1 and results:
            similarity = results[0]['similarity_score'] * 100
            plt.figtext(0.5, 0.01, f'Similarity Score: {similarity:.0f}%',
                       ha='center', fontsize=14,
                       bbox={'facecolor':'lightgrey', 'alpha':0.5, 'pad':5})

        # Save visualization
        image_name = Path(image_path).stem
        plt.tight_layout()
        plt.savefig(str(self.results_dir / f"retrieval_results_{image_name}.png"), dpi=150)
        plt.close()

        # Also create HTML result
        html_path = self.create_html_result(image_path, human_contour, results)
        return html_path

    def retrieve(self, human_contour, top_k=1):
        """Retrieve similar animal contours based on human contour using np.atleast_1d"""
        print("Calculating features...")
        human_features = self.compute_shape_features(human_contour)

        print("Searching for similar poses...")
        try:
            distances, indices = self.kdtree.query(human_features, k=top_k)
            print(f"Raw KDTree query results - distances: {distances}, type: {type(distances)}")
            print(f"Raw KDTree query results - indices: {indices}, type: {type(indices)}")
        except Exception as e:
            print(f"Error during KDTree query: {e}")
            return []

        # --- 核心修复：使用 np.atleast_1d 确保结果是可迭代的数组 ---
        distances = np.atleast_1d(distances)
        indices = np.atleast_1d(indices)
        print(f"Processed KDTree results - distances: {distances}, indices: {indices}")

        # --- 健壮性检查：确保长度匹配 ---
        if len(distances) != len(indices):
             print(f"ERROR: Mismatch in lengths between distances ({len(distances)}) and indices ({len(indices)}).")
             return []

        results = []
        # 预先检查 self.animal_db 中必要的键是否存在且为列表类型
        required_keys = ['categories', 'image_ids', 'image_paths', 'contours']
        db_valid = True
        for key in required_keys:
            if key not in self.animal_db or not isinstance(self.animal_db[key], (list, np.ndarray)):
                 print(f"CRITICAL ERROR: '{key}' is missing or not a list in self.animal_db.")
                 db_valid = False
                 break # 如果关键数据缺失，无法继续处理

        if not db_valid:
            return [] # 返回空结果

        # 获取列表长度以进行边界检查
        num_items = len(self.animal_db['contours']) # 以contours长度为基准

        print(f"Processing {len(indices)} potential matches...")
        for dist, idx in zip(distances, indices):
            # --- 将索引转换为标准 Python int ---
            try:
                current_idx = int(idx) # 转换为 Python int 以便安全索引列表
            except (ValueError, TypeError) as e:
                print(f"ERROR: Cannot convert index '{idx}' (type: {type(idx)}) to integer. Skipping. Error: {e}")
                continue

            print(f"Processing result with index: {current_idx}, distance: {dist}")
            # --- 边界检查 ---
            if 0 <= current_idx < num_items:
                try:
                    # --- 使用 current_idx (Python int) ---
                    category = self.animal_db['categories'][current_idx] if current_idx < len(self.animal_db['categories']) else "Unknown"
                    image_id = self.animal_db['image_ids'][current_idx] if current_idx < len(self.animal_db['image_ids']) else ""
                    image_path = self.animal_db['image_paths'][current_idx] if current_idx < len(self.animal_db['image_paths']) else ""
                    contour = self.animal_db['contours'][current_idx]

                    print(f"  Data retrieved - category: {category}, image_id: {image_id}")

                    results.append({
                        'category': category,
                        'image_id': image_id,
                        'image_path': image_path,
                        'contour': contour,
                        'similarity_score': 1.0 / (1.0 + dist),
                        'index': current_idx # 使用转换后的 Python int
                    })
                except IndexError:
                    print(f"Error: Index {current_idx} out of bounds for one of the lists in self.animal_db.")
                except Exception as e:
                     print(f"Error processing result at index {current_idx}: {e}")
            else:
                print(f"Error: Retrieved index {current_idx} is out of bounds (0 <= index < {num_items}).")

        print(f"Retrieve function finished, returning {len(results)} results.")
        return results

    def create_integrated_html(self, image_path=None, results=None, stage="capture"):
        """Create integrated HTML interface, including photo capture and result display"""

        # Prepare basic HTML content
        html_content = f"""<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Animal Pose Matcher</title>
    <style>
        * {{
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
        }}

        body {{
            width: 100vw;
            height: 100vh;
            overflow: hidden;
            background-color: #f5f5f5;
            display: flex;
            flex-direction: column;
        }}

        header {{
            text-align: center;
            margin-bottom: 40px;
        }}

        h1 {{
            font-size: 48px;
            margin-bottom: 20px;
        }}

        .steps {{
            display: flex;
            justify-content: space-between;
            margin-bottom: 50px;
            text-align: center;
        }}

        .step {{
            flex: 1;
            padding: 20px;
            border: 1px solid #eaeaea;
            border-radius: 10px;
            margin: 0 10px;
            background-color: white;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
        }}

        .step-number {{
            display: inline-block;
            width: 50px;
            height: 50px;
            line-height: 50px;
            background-color: #eaeaea;
            border-radius: 50%;
            margin-bottom: 20px;
            font-size: 24px;
            font-weight: bold;
        }}

        .active-step {{
            border-color: #111;
            background-color: #f0f0f0;
        }}

        .result {{
            display: flex;
            justify-content: space-between;
            margin-top: 50px;
        }}

        .result-column {{
            flex: 1;
            text-align: center;
        }}

        .match-score {{
            width: 100px;
            height: 100px;
            line-height: 100px;
            background-color: #111;
            color: white;
            border-radius: 50%;
            margin: 0 auto;
            font-size: 24px;
            font-weight: bold;
        }}

        img {{
            max-width: 100%;
            border-radius: 8px;
        }}

        .image-container {{
            border: 1px solid #eaeaea;
            border-radius: 10px;
            overflow: hidden;
            margin: 0 20px;
            height: 400px;
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: white;
        }}

        button {{
            background-color: #111;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            font-size: 16px;
            cursor: pointer;
            margin: 10px;
            transition: all 0.2s;
        }}

        button:hover {{
            opacity: 0.9;
            transform: translateY(-2px);
        }}

        button.secondary {{
            background-color: white;
            color: #111;
            border: 1px solid #111;
        }}

        .camera-container {{
            width: 100%;
            height: 400px;
            border: 1px solid #eaeaea;
            border-radius: 10px;
            overflow: hidden;
            margin: 20px auto;
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: #000;
        }}

        .camera-feed {{
            width: 100%;
            height: 100%;
            object-fit: contain;
        }}

        .controls {{
            text-align: center;
            margin-top: 20px;
        }}

        .hidden {{
            display: none;
        }}

        .countdown {{
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            font-size: 100px;
            color: white;
            text-shadow: 0 0 10px rgba(0,0,0,0.5);
            z-index: 100;
        }}

        /* New styles for the layout based on the image */
        .main-container {{
            display: flex;
            flex: 1;
            width: 100%;
            height: 100vh;
        }}

        .left-panel {{
            flex: 1;
            position: relative;
            background-color: #f0f0f0;
            overflow: hidden;
        }}

        .slideshow {{
            width: 100%;
            height: 100%;
            position: relative;
        }}

        .slideshow img {{
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            object-fit: cover;
            opacity: 0;
            transition: opacity 1s ease-in-out;
        }}

        .slideshow img.active {{
            opacity: 1;
        }}

        .right-panel {{
            flex: 1;
            display: flex;
            flex-direction: column;
            position: relative;
        }}

        .camera-overlay {{
            position: relative;
            flex: 1;
            overflow: hidden;
        }}

        .preview-label {{
            position: absolute;
            top: 10px;
            right: 10px;
            background-color: rgba(0,0,0,0.5);
            color: white;
            padding: 5px 10px;
            border-radius: 4px;
            font-size: 14px;
            z-index: 5;
        }}

        .info-overlay {{
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background-color: rgba(0,0,0,0.7);
            color: white;
            padding: 20px 30px;
            border-radius: 8px;
            text-align: center;
            z-index: 10;
        }}

        .info-overlay h2 {{
            margin-bottom: 10px;
        }}

        .step-item {{
            display: flex;
            align-items: center;
            margin: 15px 0;
            text-align: left;
        }}

        .step-circle {{
            width: 30px;
            height: 30px;
            background-color: white;
            color: #333;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-right: 15px;
            flex-shrink: 0;
        }}

        .step-text {{
            flex: 1;
        }}

        .step-text h3 {{
            margin-bottom: 5px;
        }}

        .start-button {{
            margin-top: 20px;
            background-color: white;
            color: #333;
            padding: 10px 20px;
            border-radius: 20px;
            font-weight: bold;
            cursor: pointer;
            display: inline-flex;
            align-items: center;
            border: none;
            font-size: 16px;
        }}

        .start-button:hover {{
            background-color: #f0f0f0;
        }}

        .start-button span {{
            margin-right: 5px;
        }}
    </style>
</head>
<body>
"""

        # Photo stage - completely redesigned for homepage
        if stage == "capture":
            html_content += """
    <div class="main-container">
        <div class="left-panel">
            <div class="slideshow" id="slideshow">
                <img src="/images/homepage_1.jpg" class="active" alt="Museum Image 1">
                <img src="/images/homepage_2.jpg" alt="Museum Image 2">
                <img src="/images/homepage_3.jpg" alt="Museum Image 3">
                <img src="/images/homepage_4.jpg" alt="Museum Image 4">
                <img src="/images/homepage_5.jpg" alt="Museum Image 5">
            </div>
            <div class="info-overlay">
                <h2>Animal Pose Matcher</h2>
                <div class="step-item">
                    <div class="step-circle">1</div>
                    <div class="step-text">
                        <h3>Strike a Pose</h3>
                        <p>Stand in the designated area and pose like an animal</p>
                    </div>
                </div>
                <div class="step-item">
                    <div class="step-circle">2</div>
                    <div class="step-text">
                        <h3>Take a Photo</h3>
                        <p>Our camera will capture and analyze your pose</p>
                    </div>
                </div>
                <div class="step-item">
                    <div class="step-circle">3</div>
                    <div class="step-text">
                        <h3>See Results</h3>
                        <p>Discover which animal you match with</p>
                    </div>
                </div>
                <button class="start-button" id="startExperience">
                    <span>Start Experience</span> &rarr;
                </button>
            </div>
        </div>
        <div class="right-panel">
            <div class="camera-overlay">
                <div class="preview-label">Live Preview</div>
                <video id="camera" class="camera-feed" autoplay style="width: 100%; height: 100%; object-fit: cover;"></video>
                <canvas id="captureCanvas" style="display:none;"></canvas>
                <canvas id="overlayCanvas" style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; z-index: 5; pointer-events: none;"></canvas>
            </div>
        </div>
    </div>

    <script>
        // Get elements
        const video = document.getElementById('camera');
        const canvas = document.getElementById('captureCanvas');
        const overlayCanvas = document.getElementById('overlayCanvas');
        const slideshow = document.getElementById('slideshow');
        const startBtn = document.getElementById('startExperience');
        let mediaStream = null;
        let realtimeInterval = null;

        // Access camera
        async function startCamera() {
            try {
                mediaStream = await navigator.mediaDevices.getUserMedia({
                    video: {
                        width: { ideal: 1280 },
                        height: { ideal: 720 }
                    }
                });
                video.srcObject = mediaStream;
            } catch (err) {
                console.error('Error accessing camera:', err);
                alert('Could not access camera. Please ensure you have a camera connected and have granted permission.');
            }
        }

        // Start camera
        startCamera();

        // Setup slideshow
        const slideshowImages = slideshow.querySelectorAll('img');
        let currentImageIndex = 0;

        function nextImage() {
            // Hide current image
            slideshowImages[currentImageIndex].classList.remove('active');

            // Move to next image (or back to first)
            currentImageIndex = (currentImageIndex + 1) % slideshowImages.length;

            // Show new image
            slideshowImages[currentImageIndex].classList.add('active');
        }

        // Change image every 3 seconds
        setInterval(nextImage, 3000);

        // Start realtime contour detection
        function startRealtimeContourDetection() {
            // Setup canvas - 修复轮廓显示问题
            function updateCanvasSize() {
                const videoRect = video.getBoundingClientRect();
                overlayCanvas.width = videoRect.width;
                overlayCanvas.height = videoRect.height;
                console.log(`Canvas size updated: ${overlayCanvas.width}x${overlayCanvas.height}`);
            }

            // 确保视频加载完成后立即更新画布尺寸
            if (video.readyState >= 2) {
                updateCanvasSize();
            } else {
                video.onloadeddata = updateCanvasSize;
            }

            // Listen for video size changes
            new ResizeObserver(updateCanvasSize).observe(video);

            // Start realtime detection
            realtimeInterval = setInterval(() => {
                if (!video.paused && !video.ended) {
                    const videoRect = video.getBoundingClientRect();

                    // --- 修改开始: 使用视频的原始尺寸而不是显示尺寸 ---
                    const tempCanvas = document.createElement('canvas');
                    tempCanvas.width = video.videoWidth;
                    tempCanvas.height = video.videoHeight;

                    const ctx = tempCanvas.getContext('2d');
                    ctx.drawImage(video, 0, 0, video.videoWidth, video.videoHeight);
                    // --- 修改结束 ---

                    const imageData = tempCanvas.toDataURL('image/jpeg', 0.7);

                    fetch('/contour-only', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            image: imageData
                        })
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success && data.contour) {
                            // --- 修改: 移除冗余调用 ---
                            // updateCanvasSize();

                            // --- 添加调试日志 - 验证前端和后端尺寸是否匹配 ---
                            console.log(`前端视频原始尺寸: ${video.videoWidth}x${video.videoHeight}`);
                            console.log(`后端处理图像尺寸: ${data.image_width}x${data.image_height}`);
                            if (video.videoWidth !== data.image_width || video.videoHeight !== data.image_height) {
                                console.warn('警告: 前端视频尺寸与后端处理尺寸不匹配!');
                            }

                            const overlayCtx = overlayCanvas.getContext('2d');
                            overlayCtx.clearRect(0, 0, overlayCanvas.width, overlayCanvas.height);

                            const contour = JSON.parse(data.contour);

                            overlayCtx.strokeStyle = 'green';
                            overlayCtx.lineWidth = 3;
                            overlayCtx.beginPath();

                            if (contour.length > 0) {
                                const originalWidth = data.image_width;
                                const originalHeight = data.image_height;
                                const bbox = data.bbox || [0, 0, originalWidth, originalHeight];

                                // 简化坐标变换逻辑
                                const scaleX = overlayCanvas.width / originalWidth;
                                const scaleY = overlayCanvas.height / originalHeight;
                                
                                // 记录视频元素和canvas元素的位置和尺寸关系
                                const videoRect = video.getBoundingClientRect();
                                console.log(`视频显示区域: ${JSON.stringify({
                                    left: videoRect.left, 
                                    top: videoRect.top,
                                    width: videoRect.width,
                                    height: videoRect.height
                                })}`);
                                console.log(`覆盖Canvas尺寸: ${overlayCanvas.width}x${overlayCanvas.height}`);
                                console.log(`计算的缩放比例: scaleX=${scaleX}, scaleY=${scaleY}`);
                                
                                // 检查视频元素的offsetLeft和offsetTop
                                const videoOffsetLeft = video.offsetLeft || 0;
                                const videoOffsetTop = video.offsetTop || 0;
                                console.log(`视频元素offset: left=${videoOffsetLeft}, top=${videoOffsetTop}`);
                                
                                // 记录视频内容比例和Canvas比例
                                const videoRatio = video.videoWidth / video.videoHeight;
                                const canvasRatio = overlayCanvas.width / overlayCanvas.height;
                                console.log(`视频内容比例: ${videoRatio.toFixed(3)}, Canvas比例: ${canvasRatio.toFixed(3)}`);
                                
                                // 记录第一个点的坐标变换过程
                                if(contour.length > 0) {
                                    const firstPoint = contour[0][0];
                                    console.log(`第一个轮廓点原始坐标: [${firstPoint[0]}, ${firstPoint[1]}]`);
                                }

                                for (let i = 0; i < contour.length; i++) {
                                    const point = contour[i][0];
                                    
                                    // 最终解决方案：修复坐标计算和视频比例处理
                                    // 1. 确保overlayCanvas的尺寸与视频显示区域完全一致
                                    const videoRect = video.getBoundingClientRect();
                                    
                                    // 这里确保overlayCanvas尺寸与视频显示区域相同
                                    if (overlayCanvas.width !== videoRect.width || 
                                        overlayCanvas.height !== videoRect.height) {
                                        overlayCanvas.width = videoRect.width;
                                        overlayCanvas.height = videoRect.height;
                                    }
                                    
                                    // 2. 计算视频内容在canvas中的实际显示尺寸 
                                    // (当video使用object-fit:cover时的情况)
                                    const videoRatio = video.videoWidth / video.videoHeight;
                                    const canvasRatio = overlayCanvas.width / overlayCanvas.height;
                                    
                                    let scaleFactor, xOffset = 0, yOffset = 0;
                                    
                                    if (videoRatio > canvasRatio) {
                                        // 视频更宽，高度充满，宽度溢出
                                        scaleFactor = overlayCanvas.height / video.videoHeight;
                                        xOffset = (overlayCanvas.width - video.videoWidth * scaleFactor) / 2;
                                    } else {
                                        // 视频更高，宽度充满，高度溢出
                                        scaleFactor = overlayCanvas.width / video.videoWidth;
                                        yOffset = (overlayCanvas.height - video.videoHeight * scaleFactor) / 2;
                                    }
                                    
                                    // 3. 转换轮廓坐标到canvas坐标
                                    let canvasX = point[0] * scaleFactor + xOffset;
                                    let canvasY = point[1] * scaleFactor + yOffset;
                                    
                                    // ===== 新解决方案: 精确匹配视频实际显示区域 =====
                                    // 1. 考虑视频在DOM中的物理偏移位置
                                    // (仅当视频和canvas不完全重叠时需要)
                                    const containerOffset = video.parentElement.getBoundingClientRect();
                                    const videoOffsetInContainer = {
                                        left: videoRect.left - containerOffset.left,
                                        top: videoRect.top - containerOffset.top
                                    };
                                    
                                    // 2. 如果有必要，应用偏移修正
                                    // 注意: 仅当视频和画布在同一容器但位置有偏差时需要
                                    if (Math.abs(videoOffsetInContainer.left) > 1 || 
                                        Math.abs(videoOffsetInContainer.top) > 1) {
                                        canvasX += videoOffsetInContainer.left;
                                        canvasY += videoOffsetInContainer.top;
                                        console.log(`应用容器内偏移修正: (${videoOffsetInContainer.left.toFixed(1)}, ${videoOffsetInContainer.top.toFixed(1)})`);
                                    }
                                    
                                    // 记录首个点的详细变换过程
                                    if (i === 0) {
                                        console.log(`轮廓点[0]变换: 原始=(${point[0]}, ${point[1]})`);
                                        console.log(`应用scaleFactor=${scaleFactor.toFixed(3)}: (${(point[0] * scaleFactor).toFixed(1)}, ${(point[1] * scaleFactor).toFixed(1)})`);
                                        console.log(`应用偏移(${xOffset.toFixed(1)}, ${yOffset.toFixed(1)}): 最终=(${canvasX.toFixed(1)}, ${canvasY.toFixed(1)})`);
                                        
                                        // 检查简单比例方法的结果用于对比
                                        const simpleX = point[0] * scaleX;
                                        const simpleY = point[1] * scaleY;
                                        console.log(`简单比例变换: (${simpleX.toFixed(1)}, ${simpleY.toFixed(1)})`);
                                        console.log(`坐标差异: (${(canvasX - simpleX).toFixed(1)}, ${(canvasY - simpleY).toFixed(1)})`);
                                    }
                                    
                                    if (i === 0) {
                                        overlayCtx.moveTo(canvasX, canvasY);
                                    } else {
                                        overlayCtx.lineTo(canvasX, canvasY);
                                    }
                                }

                                overlayCtx.closePath();
                                overlayCtx.stroke();
                                console.log(`绘制了轮廓，点数: ${contour.length}`);
                            }
                        }
                    })
                    .catch(error => {
                        console.error('Error getting contour:', error);
                    });
                }
            }, 200);
        }

        // Start realtime contour detection immediately
        startRealtimeContourDetection();

        // Handle start experience button
        startBtn.addEventListener('click', () => {
            window.location.href = '/take-photo.html';
        });
    </script>
"""
        # 处理阶段
        elif stage == "processing":
            html_content += """
    <div style="text-align: center; padding: 100px 0;">
        <div style="font-size: 24px; margin-bottom: 20px;">Processing your pose...</div>
        <div class="spinner" style="border: 8px solid #f3f3f3; border-top: 8px solid #111; border-radius: 50%; width: 60px; height: 60px; margin: 0 auto; animation: spin 2s linear infinite;"></div>
    </div>

    <style>
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>

    <script>
        // Poll to check if results are ready
        function checkResults() {
            fetch('/check-results')
            .then(response => response.json())
            .then(data => {
                if (data.ready) {
                    window.location.href = '/result.html';
                } else {
                    // Continue polling
                    setTimeout(checkResults, 1000);
                }
            })
            .catch(error => {
                console.error('Error checking results:', error);
                // Automatically redirect to result page if error occurs
                setTimeout(() => {
                    window.location.href = '/result.html';
                }, 3000);
            });
        }

        // Start polling
        setTimeout(checkResults, 1000);

        // Automatically redirect after 5 seconds as a backup plan
        setTimeout(() => {
            window.location.href = '/result.html';
        }, 5000);
    </script>
"""

        # Result stage
        elif stage == "result" and results and len(results) > 0:
            result = results[0]  # Use the most matching result
            animal_category = result['category'].capitalize()
            similarity_score = int(result['similarity_score'] * 100)

            # Prepare image paths - use HTTP path instead of file path
            human_img_filename = "human_pose.jpg"
            animal_img_filename = "animal_pose.jpg"
            human_img_path = str(self.results_dir / human_img_filename)
            animal_img_path = str(self.results_dir / animal_img_filename)

            if image_path and not os.path.exists(human_img_path):
                # If human and animal images haven't been created yet, create them
                self._save_contour_images(image_path, human_contour=self.extract_human_contour(image_path), result=result)

            # 添加全屏分屏样式
            html_content += f"""
    <style>
        /* 重置和基础样式 */
        body {{
            width: 100vw;
            height: 100vh;
            overflow: hidden;
            background-color: #f0f0f0;
            margin: 0;
            padding: 0;
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
        }}

        /* 结果页面容器 */
        .result-page {{
            width: 100%;
            height: 100%;
            position: relative;
            display: flex;
        }}

        /* 主体内容 */
        .main-content {{
            flex: 1;
            display: flex;
            width: 100%;
            height: 100%;
            padding: 0;
            position: relative;
        }}

        /* 图片区域 */
        .image-area {{
            position: relative;
            width: 50%;
            height: 100%;
            overflow: hidden;
        }}

        /* 圆形参考图 */

        /* 图像容器 */
        .image-container {{
            width: 100%;
            height: 100%;
            position: relative;
            overflow: hidden;
            border-radius: 8px;
            z-index: 1;
        }}

        /* 图像样式 */
        .result-image {{
            width: 100%;
            height: 100%;
            object-fit: cover;
            display: block;
        }}

        /* V0 block 标签 */
        .block-label {{
            position: absolute;
            bottom: 20px;
            left: 100px;
            background-color: rgba(220, 220, 220, 0.9);
            color: #666;
            font-size: 12px;
            padding: 4px 8px;
            border-radius: 4px;
        }}

        /* 中间分数区域 */
        .score-area {{
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            z-index: 10;
        }}

        /* 分数圆圈 */
        .score-circle {{
            width: 120px;
            height: 120px;
            background-color: rgba(0, 0, 0, 0.7);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            border: 2px solid white;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
        }}

        /* 分数文字 */
        .score-text {{
            color: white;
            font-size: 36px;
            font-weight: bold;
        }}

        /* 动物信息卡片 */
        .animal-info-card {{
            position: absolute;
            bottom: 100px;
            left: 50px;
            width: 300px;
            background-color: rgba(0, 0, 0, 0.7);
            color: white;
            padding: 20px;
            border-radius: 8px;
            z-index: 10;
        }}

        /* 卡片标题 */
        .card-title {{
            font-size: 24px;
            margin-top: 0;
            margin-bottom: 10px;
        }}

        /* 卡片描述 */
        .card-description {{
            font-size: 14px;
            line-height: 1.5;
            margin-bottom: 15px;
        }}

        /* 更多信息按钮 */
        .more-info-btn {{
            display: flex;
            align-items: center;
            gap: 8px;
            background-color: rgba(70, 70, 70, 0.7);
            border: none;
            color: white;
            padding: 10px 15px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            width: 100%;
        }}

        /* 位置按钮 */
        .location-btn {{
            position: absolute;
            bottom: 25px;
            left: 50px;
            display: flex;
            align-items: center;
            background-color: rgba(0, 0, 0, 0.7);
            color: white;
            padding: 10px 15px;
            border-radius: 5px;
            gap: 8px;
            cursor: pointer;
            z-index: 10;
        }}

        /* 位置图标 */
        .location-icon {{
            font-size: 16px;
        }}

        /* 位置文本 */
        .location-text {{
            font-size: 14px;
        }}

        /* Your Pose 标签 */
        .pose-label {{
            position: absolute;
            bottom: 25px;
            right: 50px;
            background-color: rgba(0, 0, 0, 0.7);
            color: white;
            padding: 10px 15px;
            border-radius: 5px;
            font-size: 14px;
            z-index: 10;
        }}

        /* 控制按钮区域 */
        .control-area {{
            position: absolute;
            bottom: 25px;
            left: 0;
            right: 0;
            display: flex;
            justify-content: center;
            gap: 15px;
            z-index: 10;
        }}

        /* 按钮样式 */
        .control-btn {{
            background-color: rgba(0, 0, 0, 0.7);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 8px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }}

        .control-btn:hover {{
            background-color: rgba(40, 40, 40, 0.7);
        }}

        /* 动物信息模态框样式 */
        #animalInfoModal {{
            display: none;
            position: fixed;
            inset: 0;
            background-color: rgba(0, 0, 0, 0.7);
            backdrop-filter: blur(8px);
            z-index: 1000;
            align-items: center;
            justify-content: center;
            padding: 20px;
            overflow-y: auto;
        }}

        .modal-content {{
            background-color: white;
            border-radius: 12px;
            max-width: 700px;
            width: 100%;
            padding: 20px;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.5);
            color: #333;
            overflow-y: auto;
            max-height: 90vh;
        }}

        .modal-header {{
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }}

        .modal-title {{
            font-size: 32px;
            font-weight: bold;
            display: flex;
            align-items: center;
            gap: 10px;
            color: #000;
        }}

        .status-tag {{
            background-color: #D97706;
            color: white;
            padding: 4px 12px;
            border-radius: 9999px;
            font-size: 14px;
            font-weight: normal;
        }}

        .close-btn {{
            background: none;
            border: none;
            color: #888;
            font-size: 32px;
            cursor: pointer;
        }}

        .modal-body {{
            color: #333;
            line-height: 1.6;
        }}

        .section-title {{
            margin-top: 20px;
            margin-bottom: 10px;
            font-size: 18px;
            font-weight: 600;
            color: #333;
        }}

        .info-grid {{
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin: 20px 0;
        }}

        .fun-fact {{
            background-color: #f3f4f6;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
        }}

        .fun-fact-title {{
            font-weight: 600;
            margin-bottom: 5px;
            color: #333;
        }}

        .location-card {{
            background-color: #f3f4f6;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
        }}

        .location-header {{
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 10px;
            font-weight: 600;
        }}

        .map-placeholder {{
            background-color: #ddd;
            height: 150px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 8px;
            color: #888;
            margin: 20px 0;
        }}

        .close-button {{
            width: 100%;
            background-color: #444;
            color: white;
            border: none;
            padding: 12px;
            border-radius: 8px;
            cursor: pointer;
            margin-top: 20px;
            font-size: 16px;
            transition: background-color 0.2s;
        }}

        .close-button:hover {{
            background-color: #333;
        }}
    </style>

    <div class="result-page">
        <div class="main-content">
            <!-- 左侧动物图像 -->
            <div class="image-area">
                <div class="image-container">
                    <img src="/images/animal_pose.jpg" alt="Animal" class="result-image">
                </div>
            </div>

            <!-- 中间分数区域 -->
            <div class="score-area">
                <div class="score-circle">
                    <span class="score-text">{similarity_score}%</span>
                </div>
            </div>

            <!-- 右侧人类图像 -->
            <div class="image-area">
                <div class="image-container">
                    <img src="/images/human_pose.jpg" alt="Your Pose" class="result-image">
                </div>
            </div>
        </div>

        <!-- 动物信息卡片 -->
        <div class="animal-info-card">
            <h2 class="card-title">{animal_category}</h2>
            <p class="card-description">This {animal_category.lower()} matched your pose with {similarity_score}% similarity.</p>
            <button class="more-info-btn" onclick="showAnimalInfo()">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <circle cx="12" cy="12" r="10"></circle>
                    <line x1="12" y1="16" x2="12" y2="12"></line>
                    <line x1="12" y1="8" x2="12.01" y2="8"></line>
                </svg>
                More Information
            </button>
        </div>

        <!-- 位置按钮 -->
        <div class="location-btn">
            <span class="location-icon">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="white" stroke-width="2">
                    <path d="M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z"></path>
                    <circle cx="12" cy="10" r="3"></circle>
                </svg>
            </span>
            <span class="location-text">Find in Museum</span>
        </div>

        <!-- Your Pose 标签 -->
        <div class="pose-label">Your Pose</div>

        <!-- 控制按钮区域 -->
        <div class="control-area">
            <button class="control-btn" onclick="window.location='/'">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="white" stroke-width="2">
                    <polyline points="15 18 9 12 15 6"></polyline>
                </svg>
                Try Again
            </button>
            <button class="control-btn" onclick="saveResult()">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="white" stroke-width="2">
                    <path d="M19 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11l5 5v11a2 2 0 0 1-2 2z"></path>
                    <polyline points="17 21 17 13 7 13 7 21"></polyline>
                    <polyline points="7 3 7 8 15 8"></polyline>
                </svg>
                Save Result
            </button>
        </div>
    </div>

    <!-- 动物信息模态框 -->
    <div id="animalInfoModal">
        <div class="modal-content">
            <div class="modal-header">
                <h2 class="modal-title">
                    {animal_category}
                    <span class="status-tag">Vulnerable</span>
                </h2>
                <button class="close-btn" onclick="hideAnimalInfo()">&times;</button>
            </div>
            <div class="modal-body">
                <h3 class="section-title">Photos</h3>
                <div class="unsplash-photos">
                    <p class="loading-photos">Loading photos from Unsplash...</p>
                    <!-- Photos from Unsplash will be loaded here -->
                </div>

                <h3 class="section-title">About</h3>
                <p class="animal-description">
                    This magnificent animal is part of our museum collection. Visit the exhibit to learn more about its habitat, behavior, and conservation status.
                </p>

                <div class="info-grid">
                    <div class="info-section">
                        <h3 class="section-title">Diet</h3>
                        <p>Varies by species</p>
                    </div>

                    <div class="info-section">
                        <h3 class="section-title">Habitat</h3>
                        <p>Natural habitats across Africa</p>
                    </div>

                    <div class="info-section">
                        <h3 class="section-title">Lifespan</h3>
                        <p>15-30 years in the wild</p>
                    </div>

                    <div class="info-section">
                        <h3 class="section-title">Weight</h3>
                        <p>Varies by species and age</p>
                    </div>
                </div>

                <div class="fun-fact">
                    <div class="fun-fact-title">Fun Fact</div>
                    <p>This animal is part of a critical conservation effort at our museum.</p>
                </div>

                <h3 class="section-title">Museum Location</h3>
                <div class="location-card">
                    <div class="location-header">
                        <div class="location-icon">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <path d="M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z"></path>
                                <circle cx="12" cy="10" r="3"></circle>
                            </svg>
                        </div>
                        <span>Main Exhibition Hall</span>
                    </div>
                    <p>Follow the signs or ask a museum guide for directions to this exhibit.</p>
                </div>

                <div class="map-placeholder">
                    Museum Map Placeholder
                </div>

                <button class="close-button" onclick="hideAnimalInfo()">Close</button>
            </div>
        </div>
    </div>

    <script>
        function saveResult() {{
            alert('Result saved!');
        }}

        function findInMuseum() {{
            alert('Locating {animal_category} in the museum: African Mammals, Floor 1, South Wing');
        }}

        function showAnimalInfo() {{
            // 从API获取动物信息
            fetch(`/api/animal-info?name={animal_category}`)
                .then(response => response.json())
                .then(data => {{
                    if (data.success) {{
                        const animalInfo = data.data;

                        // 设置动物信息
                        const animalTitle = document.querySelector('.modal-title');
                        if (animalTitle) {{
                            animalTitle.innerHTML = '{animal_category} <span class="status-tag">' + (animalInfo.conservation_status || "Vulnerable") + '</span>';
                        }}

                        // 更新动物描述
                        const animalDescription = document.querySelector('.animal-description');
                        if (animalDescription) {{
                            animalDescription.textContent = animalInfo.description ||
                                'This magnificent animal is part of our museum collection. Visit the exhibit to learn more about its habitat, behavior, and conservation status.';
                        }}

                        // 设置信息卡片内容
                        const dietSection = document.querySelector('.info-grid .info-section:nth-child(1) p');
                        const habitatSection = document.querySelector('.info-grid .info-section:nth-child(2) p');
                        const lifespanSection = document.querySelector('.info-grid .info-section:nth-child(3) p');
                        const weightSection = document.querySelector('.info-grid .info-section:nth-child(4) p');

                        if (dietSection) dietSection.textContent = animalInfo.diet || 'Varies by species';
                        if (habitatSection) habitatSection.textContent = animalInfo.habitat || 'Natural habitats across Africa';
                        if (lifespanSection) lifespanSection.textContent = animalInfo.lifespan || '15-30 years in the wild';
                        if (weightSection) weightSection.textContent = animalInfo.weight || 'Varies by species and age';

                        // 设置有趣的事实
                        const funFactText = document.querySelector('.fun-fact p');
                        if (funFactText) {{
                            funFactText.textContent = animalInfo.fun_fact ||
                                'This animal is part of a critical conservation effort at our museum.';
                        }}

                        // 设置博物馆位置信息
                        const locationInfo = {{
                            'Giraffe': 'African Mammals, Floor 1, South Wing',
                            'Elephant': 'African Mammals, Floor 1, Central Hall',
                            'Lion': 'African Predators, Floor 2, East Wing',
                            'Zebra': 'African Plains, Floor 1, West Wing',
                            'Rhino': 'Endangered Species, Floor 3, North Wing'
                        }};

                        const locationSpan = document.querySelector('.location-header span');
                        if (locationSpan) {{
                            locationSpan.textContent = locationInfo['{animal_category}'] || 'Main Exhibition Hall';
                        }}

                        // 加载Unsplash图片
                        loadUnsplashPhotos('{animal_category}');

                        document.getElementById('animalInfoModal').style.display = 'flex';
                    }} else {{
                        console.error('获取动物信息失败:', data.error);
                        // 如果API调用失败，使用预定义的备用数据
                        setFallbackAnimalInfo();

                        // 显示模态框
                        document.getElementById('animalInfoModal').style.display = 'flex';
                    }}
                }})
                .catch(error => {{
                    console.error('API请求出错:', error);
                    // 如果API调用出错，使用预定义的备用数据
                    setFallbackAnimalInfo();

                    // 显示模态框
                    document.getElementById('animalInfoModal').style.display = 'flex';
                }});
        }}

        // 备用函数，当API请求失败时使用
        function setFallbackAnimalInfo() {{
            // 设置动物信息
            const animalTitle = document.querySelector('.modal-title');
            if (animalTitle) {{
                animalTitle.innerHTML = '{animal_category} <span class="status-tag">Vulnerable</span>';
            }}

            // 更新动物描述（备用数据）
            const animalDescriptions = {{
                'Giraffe': 'The giraffe is the tallest living terrestrial animal. Its distinctive features are its extremely long neck and legs, horn-like ossicones, and spotted coat patterns. Giraffes have specialized cardiovascular systems to manage blood pressure between their brain and heart.',
                'Elephant': 'Elephants are the largest existing land animals and are known for their intelligence, long trunks, and tusks. They have complex social structures and are highly intelligent animals with excellent memory.',
                'Lion': 'Lions are large cats belonging to the genus Panthera. They have muscular, deep-chested bodies and short, rounded heads. The male lion is distinguished by his mane, which is absent in female lions.',
                'Zebra': 'Zebras are African equines with distinctive black-and-white striped coats. Each zebra has its own unique pattern of stripes, as distinctive as human fingerprints.',
                'Rhino': 'Rhinoceroses are large, herbivorous mammals identified by their characteristic horned snouts. Their thick protective skin is formed from layers of collagen.'
            }};

            const animalDescription = document.querySelector('.animal-description');
            if (animalDescription) {{
                animalDescription.textContent = animalDescriptions['{animal_category}'] ||
                    'This magnificent animal is part of our museum collection. Visit the exhibit to learn more about its habitat, behavior, and conservation status.';
            }}

            // 设置信息卡片内容（备用数据）
            const dietInfo = {{
                'Giraffe': 'Herbivore - primarily acacia leaves and shoots',
                'Elephant': 'Herbivore - up to 300kg of vegetation daily',
                'Lion': 'Carnivore - primarily large ungulates',
                'Zebra': 'Herbivore - mainly grasses',
                'Rhino': 'Herbivore - grasses, leaves, fruits'
            }};

            const habitatInfo = {{
                'Giraffe': 'Savannas, grasslands, and open woodlands',
                'Elephant': 'Forests, deserts, marshes, and savannas',
                'Lion': 'Grasslands, savannas, and open woodlands',
                'Zebra': 'Plains, grasslands, and light woodlands',
                'Rhino': 'Grasslands and floodplains'
            }};

            const lifespanInfo = {{
                'Giraffe': '20-25 years in the wild',
                'Elephant': '60-70 years',
                'Lion': '10-14 years in the wild',
                'Zebra': '25-30 years in the wild',
                'Rhino': '35-50 years'
            }};

            const weightInfo = {{
                'Giraffe': '800-1,930 kg (1,760-4,250 lb)',
                'Elephant': '2,700-6,000 kg (6,000-13,000 lb)',
                'Lion': '150-250 kg (330-550 lb)',
                'Zebra': '350-450 kg (770-990 lb)',
                'Rhino': '1,800-2,700 kg (4,000-6,000 lb)'
            }};

            const dietSection = document.querySelector('.info-grid .info-section:nth-child(1) p');
            const habitatSection = document.querySelector('.info-grid .info-section:nth-child(2) p');
            const lifespanSection = document.querySelector('.info-grid .info-section:nth-child(3) p');
            const weightSection = document.querySelector('.info-grid .info-section:nth-child(4) p');

            if (dietSection) dietSection.textContent = dietInfo['{animal_category}'] || 'Varies by species';
            if (habitatSection) habitatSection.textContent = habitatInfo['{animal_category}'] || 'Natural habitats across Africa';
            if (lifespanSection) lifespanSection.textContent = lifespanInfo['{animal_category}'] || '15-30 years in the wild';
            if (weightSection) weightSection.textContent = weightInfo['{animal_category}'] || 'Varies by species and age';

            // 设置有趣的事实（备用数据）
            const funFacts = {{
                'Giraffe': 'Giraffes only need 5-30 minutes of sleep in a 24-hour period, often taken in very short naps.',
                'Elephant': 'Elephants can recognize themselves in mirrors, showing self-awareness that few animals possess.',
                'Lion': 'Lions can roar so loudly it can be heard up to 8 kilometers away.',
                'Zebra': 'Each zebra has a unique stripe pattern, like human fingerprints.',
                'Rhino': 'Rhinos are known to have poor eyesight but excellent hearing and sense of smell.'
            }};

            const funFactText = document.querySelector('.fun-fact p');
            if (funFactText) {{
                funFactText.textContent = funFacts['{animal_category}'] ||
                    'This animal is part of a critical conservation effort at our museum.';
            }}
        }}

        function hideAnimalInfo() {{
            document.getElementById('animalInfoModal').style.display = 'none';
        }}

        function showMuseumMap(event) {{
            // 阻止事件冒泡，这样点击location-link不会触发父元素info-btn的点击事件
            if (event) {{
                event.stopPropagation();
            }}
            alert('Museum map for {animal_category} location');
        }}

        function loadUnsplashPhotos(animalName) {{
            const photosContainer = document.querySelector('.unsplash-photos');
            if (!photosContainer) return;

            // 显示加载中状态
            photosContainer.innerHTML = '<p style="text-align:center; padding:20px; color:#666; font-style:italic;">Loading photos from Unsplash...</p>';

            // 从API获取Unsplash图片
            fetch(`/api/unsplash-images?name=${{animalName}}`)
                .then(response => response.json())
                .then(data => {{
                    if (data.success && data.images && data.images.length > 0) {{
                        // 清除加载消息
                        photosContainer.innerHTML = '';

                        // 创建水平滚动容器
                        const photoGrid = document.createElement('div');
                        photoGrid.style.display = 'flex';
                        photoGrid.style.overflowX = 'auto';
                        photoGrid.style.scrollBehavior = 'smooth';
                        photoGrid.style.padding = '10px 0';
                        photoGrid.style.gap = '15px';
                        photoGrid.style.marginBottom = '15px';

                        // 添加每张图片
                        data.images.forEach(image => {{
                            const photoItem = document.createElement('div');
                            photoItem.style.flex = '0 0 auto';
                            photoItem.style.width = '250px';
                            photoItem.style.borderRadius = '8px';
                            photoItem.style.overflow = 'hidden';

                            const img = document.createElement('img');
                            img.src = image.url;
                            img.alt = `Photo of ${{animalName}}`;
                            img.style.width = '100%';
                            img.style.height = '180px';
                            img.style.objectFit = 'cover';
                            img.style.borderRadius = '8px';
                            img.style.transition = 'transform 0.3s';

                            // 鼠标悬停效果
                            img.onmouseover = function() {{ this.style.transform = 'scale(1.03)'; }};
                            img.onmouseout = function() {{ this.style.transform = 'scale(1)'; }};

                            const attribution = document.createElement('div');
                            attribution.style.fontSize = '12px';
                            attribution.style.padding = '8px 0';
                            attribution.style.textAlign = 'center';
                            attribution.style.color = '#666';
                            attribution.innerHTML = `Photo by <a href="${{image.photographer_url}}" style="color:#333; text-decoration:none;" target="_blank" rel="noopener noreferrer">${{image.photographer}}</a> on <a href="https://unsplash.com/?utm_source=animal_pose_matcher&utm_medium=referral" style="color:#333; text-decoration:none;" target="_blank" rel="noopener noreferrer">Unsplash</a>`;

                            photoItem.appendChild(img);
                            photoItem.appendChild(attribution);
                            photoGrid.appendChild(photoItem);
                        }});

                        // 添加简单的滚动提示
                        const scrollHint = document.createElement('div');
                        scrollHint.style.textAlign = 'center';
                        scrollHint.style.fontSize = '12px';
                        scrollHint.style.color = '#666';
                        scrollHint.style.marginTop = '5px';
                        scrollHint.innerHTML = '← Swipe left or right to view more photos →';

                        photosContainer.appendChild(photoGrid);
                        photosContainer.appendChild(scrollHint);
                    }} else {{
                        photosContainer.innerHTML = '<p style="text-align:center; padding:20px; color:#666; font-style:italic;">No photos found.</p>';
                    }}
                }})
                .catch(error => {{
                    console.error('Error loading Unsplash photos:', error);
                    photosContainer.innerHTML = '<p style="text-align:center; padding:20px; color:#666; font-style:italic;">Failed to load photos</p>';
                }});
        }}
    </script>
</body>
</html>"""

        # Close HTML
        html_content += """
</body>
</html>"""

        return html_content

    def _save_contour_images(self, image_path, human_contour, result):
        """Save contour images for result presentation. MUST find a valid animal image."""
        human_img_path = str(self.results_dir / "human_pose.jpg") # 提前定义 human_img_path
        animal_img_path = None # 初始化为 None

        # --- 人体图像处理部分 ---
        try:
            # 使用最新传入的图像路径
            print(f"Processing human image from path: {image_path}")
            query_img = cv2.imread(str(image_path))
            if query_img is None:
                print(f"ERROR: Could not load query image: {image_path}")
                return human_img_path, None

            query_img = cv2.cvtColor(query_img, cv2.COLOR_BGR2RGB)

            # 截取摄像头图像中央1:1区域，确保图片比例一致
            h, w = query_img.shape[:2]
            if w > h:
                # 宽大于高，裁剪宽度
                crop_size = h
                start_x = (w - crop_size) // 2
                cropped_img = query_img[:, start_x:start_x+crop_size]
            else:
                # 高大于宽，裁剪高度
                crop_size = w
                start_y = (h - crop_size) // 2
                cropped_img = query_img[start_y:start_y+crop_size, :]

            # 使用裁剪后的图像
            overlay_img = cropped_img.copy()
            img_h, img_w = overlay_img.shape[:2]

            # 调整轮廓大小并居中放置
            resize_factor = min(img_h, img_w) / 100.0  # 根据图像大小自适应缩放
            resized_contour = (human_contour * resize_factor).astype(np.int32)

            # 修复人物轮廓定位，使其居中
            x, y, w, h = cv2.boundingRect(resized_contour)
            center_offset_x = (img_w - w) // 2 - x
            center_offset_y = (img_h - h) // 2 - y

            centered_contour = resized_contour + [center_offset_x, center_offset_y]
            cv2.drawContours(overlay_img, [centered_contour], -1, (0, 255, 0), 2)

            # 保存人物图像
            plt.imsave(human_img_path, overlay_img)
            print(f"Human image saved to: {human_img_path}")
        except Exception as e:
            print(f"ERROR processing human image: {e}")
            import traceback
            traceback.print_exc()
            # 尽管人体图像处理失败，我们仍然继续尝试处理动物图像

        # --- 动物图像处理部分 ---
        try:
            # 获取目标类别和ID
            target_category = result.get('category', '').lower()
            target_image_id = result.get('image_id')

            print(f"Processing animal image: category='{target_category}', image_id='{target_image_id}'")

            if not target_category and not target_image_id:
                print("ERROR: Result has neither category nor image_id information")
                return human_img_path, None

            # 获取museum和label museum文件夹
            museum_dir = Path('/Users/<USER>/Desktop/museum')
            label_museum_dir = Path('/Users/<USER>/Desktop/label museum')

            if not museum_dir.exists() or not label_museum_dir.exists():
                print(f"CRITICAL ERROR: Museum directories not found")
                return human_img_path, None

            # 步骤1: 先尝试通过image_id直接定位图片
            found_img_path = None

            if target_image_id:
                # 搜索所有可能的图片扩展名
                for ext in ['.jpg', '.jpeg', '.png', '.JPG', '.JPEG', '.PNG']:
                    potential_path = museum_dir / f"{target_image_id}{ext}"
                    if potential_path.exists():
                        found_img_path = potential_path
                        print(f"Found image with ID '{target_image_id}': {found_img_path}")
                        break

            # 步骤2: 如果通过ID没找到，则通过类别搜索
            if not found_img_path and target_category:
                print(f"Searching for images matching category '{target_category}'...")

                # 获取所有有效的图片-JSON对
                valid_pairs = []
                for img_file in sorted(museum_dir.glob('*.*')):
                    if img_file.suffix.lower() not in ['.jpg', '.jpeg', '.png']:
                        continue

                    json_path = label_museum_dir / f"{img_file.stem}.json"
                    if json_path.exists():
                        try:
                            with open(json_path, 'r') as f:
                                label_data = json.load(f)

                            # 获取第一个shape的label
                            if 'shapes' in label_data and len(label_data['shapes']) > 0:
                                label = label_data['shapes'][0].get('label', '').lower()
                                if label == target_category:
                                    valid_pairs.append((img_file, json_path, label))
                        except Exception as e:
                            print(f"Error reading JSON file {json_path}: {e}")

                # 如果找到匹配的图片，随机选择一个
                if valid_pairs:
                    # 使用image_id作为随机种子，确保结果可重现
                    seed = int(hashlib.md5(str(target_image_id).encode()).hexdigest(), 16) if target_image_id else 0
                    random.seed(seed)
                    selected_pair = random.choice(valid_pairs)
                    found_img_path = selected_pair[0]
                    print(f"Selected image {found_img_path.name} with label '{selected_pair[2]}'")

            # 验证是否找到图片
            if not found_img_path:
                print(f"CRITICAL ERROR: Could not find any valid image")
                return human_img_path, None

            # 步骤3: 从标注文件中获取精确轮廓
            json_path = label_museum_dir / f"{found_img_path.stem}.json"
            animal_type = None
            animal_mask = None

            if json_path.exists():
                try:
                    with open(json_path, 'r') as f:
                        label_data = json.load(f)

                    if 'shapes' in label_data and len(label_data['shapes']) > 0:
                        shape = label_data['shapes'][0]
                        animal_type = shape.get('label', '').lower()

                        # 创建掩码
                        img_h = label_data.get('imageHeight', 0)
                        img_w = label_data.get('imageWidth', 0)

                        if img_h > 0 and img_w > 0 and 'points' in shape:
                            # 转换points为Numpy数组
                            points = np.array(shape['points'], dtype=np.int32)
                            mask = np.zeros((img_h, img_w), dtype=np.uint8)

                            # 根据形状类型填充掩码
                            if shape['shape_type'] == 'polygon':
                                cv2.fillPoly(mask, [points], 1)
                            elif shape['shape_type'] == 'rectangle':
                                cv2.rectangle(mask, tuple(points[0]), tuple(points[1]), 1, -1)

                            animal_mask = mask
                except Exception as e:
                    print(f"Error parsing JSON file {json_path}: {e}")

            # 确保获取到了动物类型
            if not animal_type:
                # 如果JSON解析失败，退回到使用目标类别
                animal_type = target_category

            # 更新结果中的类别（确保一致性）
            result['category'] = animal_type
            print(f"Final animal category: '{animal_type}'")

            # 步骤4: 加载和处理动物图片
            animal_img = cv2.imread(str(found_img_path))
            if animal_img is None:
                print(f"CRITICAL ERROR: Failed to load animal image: {found_img_path}")
                return human_img_path, None

            animal_img = cv2.cvtColor(animal_img, cv2.COLOR_BGR2RGB)
            animal_overlay = animal_img.copy()
            animal_h, animal_w = animal_img.shape[:2]

            # --- 绘制轮廓（优先使用从JSON文件获取的掩码） ---
            if animal_mask is not None:
                print("Using mask from JSON file to draw contour")
                # 将掩码转换为轮廓
                animal_mask = (animal_mask * 255).astype(np.uint8)
                contours, _ = cv2.findContours(animal_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

                if contours:
                    # 绘制最大的轮廓
                    max_contour = max(contours, key=cv2.contourArea)
                    cv2.drawContours(animal_overlay, [max_contour], -1, (0, 255, 0), 2)
                else:
                    print("Warning: Could not extract contours from mask")
            else:
                # 使用 .pkl 文件中的轮廓数据（仅作为后备）
                print("Falling back to using contour from .pkl file")
                animal_contour = result.get('contour')
                if animal_contour is not None and len(animal_contour) > 0:
                     # 使用与人体轮廓类似的缩放和居中逻辑
                     contour_area = cv2.contourArea(animal_contour)
                     if contour_area > 10:
                          resize_factor_h = animal_h / (cv2.boundingRect(animal_contour)[3] + 1e-6)
                          resize_factor_w = animal_w / (cv2.boundingRect(animal_contour)[2] + 1e-6)
                          resize_factor = min(resize_factor_h, resize_factor_w) * 0.8 # 缩放并留边距
                          resize_factor = max(0.1, min(resize_factor, 5.0)) # 限制缩放范围

                          resized_animal_contour = (animal_contour * resize_factor).astype(np.int32)

                          try:
                              x, y, w, h = cv2.boundingRect(resized_animal_contour)
                              animal_offset_x = (animal_w - w) // 2 - x
                              animal_offset_y = (animal_h - h) // 2 - y
                              centered_animal_contour = resized_animal_contour + [animal_offset_x, animal_offset_y]
                              cv2.drawContours(animal_overlay, [centered_animal_contour], -1, (0, 255, 0), 2)
                          except Exception as e:
                               print(f"Warning: Error drawing animal contour: {e}")
                     else:
                         print("Warning: Animal contour is too small or invalid, not drawing.")
                else:
                    print("Warning: Animal contour data is missing or invalid, not drawing.")

            # 保存最终图像
            animal_img_filename = "animal_pose.jpg"
            animal_img_path_out = str(self.results_dir / animal_img_filename)
            plt.imsave(animal_img_path_out, animal_overlay)
            animal_img_path = animal_img_path_out

        except Exception as e:
            print(f"ERROR processing animal image: {e}")
            import traceback
            traceback.print_exc()
            return human_img_path, None

        # 返回处理后的路径
        return human_img_path, animal_img_path

    def _fallback_animal_image(self, img_path, animal_img_path, result):
        """使用YOLO作为备选方法处理动物图像"""
        # 识别museum图片中的动物
        animal_type, animal_mask = self.detect_animal_in_image(img_path)

        # 注意：不再覆盖retrieve方法确定的结果，确保动物类别完全由几何轮廓匹配决定
        # 原代码: result['category'] = animal_type (已删除)

        # 记录检测到的动物类型，但不影响匹配结果
        detected_animal = animal_type  # 只用于日志记录
        print(f"Retrieved result category: {result['category']}, Detected animal: {detected_animal}")

        animal_img = cv2.imread(img_path)
        animal_img = cv2.cvtColor(animal_img, cv2.COLOR_BGR2RGB)

        # Create animal image contour overlay
        animal_overlay = animal_img.copy()

        # 如果有动物掩码，使用它绘制轮廓
        if animal_mask is not None:
            animal_mask = (animal_mask * 255).astype(np.uint8)
            contours, _ = cv2.findContours(animal_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            if contours:
                # 绘制最大的轮廓
                max_contour = max(contours, key=cv2.contourArea)
                cv2.drawContours(animal_overlay, [max_contour], -1, (0, 255, 0), 2)
        else:
            # 使用原始的动物轮廓
            animal_h, animal_w = animal_img.shape[:2]
            animal_resize_factor = min(animal_h, animal_w) / 100.0
            animal_contour = result['contour']
            resized_animal_contour = (animal_contour * animal_resize_factor).astype(np.int32)

            # 修复动物轮廓定位
            x, y, w, h = cv2.boundingRect(resized_animal_contour)
            animal_offset_x = (animal_w - w) // 2 - x
            animal_offset_y = (animal_h - h) // 2 - y

            centered_animal_contour = resized_animal_contour + [animal_offset_x, animal_offset_y]
            cv2.drawContours(animal_overlay, [centered_animal_contour], -1, (0, 255, 0), 2)

        # Save animal image
        plt.imsave(animal_img_path, animal_overlay)

    def detect_animal_in_image(self, image_path):
        """使用label museum中的标注文件获取动物类型和分割掩码"""
        try:
            # 获取图像文件名（不带路径和扩展名）
            image_filename = os.path.basename(image_path)
            image_name = os.path.splitext(image_filename)[0]

            # 查找对应的JSON标注文件
            label_museum_dir = Path('/Users/<USER>/Desktop/label museum')
            json_file_path = label_museum_dir / f"{image_name}.json"

            # 如果没有找到对应的标注文件，返回默认结果
            if not json_file_path.exists():
                print(f"未找到标注文件：{json_file_path}")
                return "未标注动物", None

            # 读取图像获取尺寸
            orig_img = cv2.imread(image_path)
            if orig_img is None:
                return "无法读取图像", None

            orig_h, orig_w = orig_img.shape[:2]

            # 读取JSON标注文件
            import json
            with open(json_file_path, 'r') as f:
                data = json.load(f)

            # 提取标签和多边形点
            shapes = data.get('shapes', [])
            if not shapes:
                return "未标注动物", None

            # 使用第一个标签作为动物名称
            animal_type = shapes[0].get('label', "未知动物")

            # 创建掩码图像
            mask = np.zeros((orig_h, orig_w), dtype=np.uint8)

            # 处理所有相同标签的多边形，将它们合并到一个掩码中
            for shape in shapes:
                if shape.get('label') == animal_type and shape.get('shape_type') == 'polygon':
                    # 提取多边形点坐标
                    points = np.array(shape.get('points', []), dtype=np.int32)
                    if len(points) < 3:  # 至少需要3个点才能形成多边形
                        continue

                    # 在掩码上绘制多边形
                    cv2.fillPoly(mask, [points], 255)

            return animal_type, mask

        except Exception as e:
            print(f"标注文件处理出错: {e}")
            import traceback
            traceback.print_exc()
            return "处理错误", None


class CameraRetrieval:
    """Camera capture and pose retrieval class"""
    def __init__(self, retrieval_system):
        """Initialize camera retrieval system"""
        self.retrieval = retrieval_system

    def display_countdown(self, cap, seconds=3):
        """Display countdown and capture image"""
        font = cv2.FONT_HERSHEY_SIMPLEX
        font_scale = 5
        font_thickness = 5
        font_color = (0, 255, 0)  # Green

        for i in range(seconds, 0, -1):
            start_time = time.time()

            while time.time() - start_time < 1:
                ret, frame = cap.read()
                if not ret:
                    return None

                # Get frame size
                height, width = frame.shape[:2]

                # Calculate text size
                text_size = cv2.getTextSize(str(i), font, font_scale, font_thickness)[0]
                text_x = (width - text_size[0]) // 2
                text_y = (height + text_size[1]) // 2

                # Display countdown
                countdown_frame = frame.copy()
                cv2.putText(countdown_frame, str(i), (text_x, text_y),
                          font, font_scale, font_color, font_thickness)

                cv2.imshow('Camera', countdown_frame)

                if cv2.waitKey(10) & 0xFF == ord('q'):
                    return None

        # Display "Take Photo!"
        start_time = time.time()
        while time.time() - start_time < 0.5:  # Brief display
            ret, frame = cap.read()
            if not ret:
                return None

            # Get frame size (for "Take Photo!")
            height, width = frame.shape[:2]

            # Calculate text size (for "Take Photo!")
            text = "Take Photo!"
            text_size = cv2.getTextSize(text, font, font_scale*0.8, font_thickness)[0]
            text_x = (width - text_size[0]) // 2
            text_y = (height + text_size[1]) // 2

            # Display "Take Photo!"
            capture_frame = frame.copy()
            cv2.putText(capture_frame, text, (text_x, text_y),
                      font, font_scale*0.8, (0, 0, 255), font_thickness)

            cv2.imshow('Camera', capture_frame)
            cv2.waitKey(1)

    def capture_and_retrieve(self, countdown=3):
        """Open camera, execute countdown, capture image, and retrieve similar poses"""
        cap = cv2.VideoCapture(0)

        if not cap.isOpened():
            print("Unable to open camera")
            return

        ret, frame = cap.read()
        if not ret:
            print("Unable to get frame")
            return

        # Display camera view and wait for user to press space bar to take photo
        print("Press space bar to start photo countdown, press 'q' to exit")
        while True:
            ret, frame = cap.read()
            if not ret:
                break

            # Display instruction text
            cv2.putText(frame, "Press space bar to start photo countdown", (50, 50),
                      cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 0), 2)
            cv2.putText(frame, "Press 'q' to exit", (50, 100),
                      cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 0), 2)

            cv2.imshow('Camera', frame)

            key = cv2.waitKey(1)
            if key == ord('q'):
                cap.release()
                cv2.destroyAllWindows()
                return
            elif key == ord(' '):
                break

        # Execute countdown
        self.display_countdown(cap, countdown)

        # Take photo
        print("Taking photo...")
        ret, frame = cap.read()
        if not ret:
            print("Unable to get frame")
            cap.release()
            cv2.destroyAllWindows()
            return

        # 生成唯一的时间戳文件名，确保每次拍照都使用新图像
        timestamp = int(time.time())
        img_path = self.retrieval.results_dir / f"captured_image_{timestamp}.jpg"

        # 保存图像
        cv2.imwrite(str(img_path), frame)
        print(f"Image saved to {img_path}")

        # 清理之前的临时图像文件
        for old_file in self.retrieval.results_dir.glob("captured_image_*.jpg"):
            if old_file != img_path and os.path.exists(old_file):
                try:
                    os.remove(old_file)
                    print(f"Removed old capture: {old_file}")
                except:
                    pass

        # Release camera resources
        cap.release()
        cv2.destroyAllWindows()

        print("Processing image and retrieving similar poses...")
        # Use ContourRetrieval to extract contour and retrieve
        try:
            human_contour = self.retrieval.extract_human_contour(img_path)

            if human_contour is not None:
                results = self.retrieval.retrieve(human_contour, top_k=1)
                if results:
                    # 创建结果文件
                    html_path = str(self.retrieval.results_dir / "result.html")

                    # 使用浏览器打开结果
                    if os.path.exists(html_path):
                        print(f"Opening result in browser: {html_path}")
                        webbrowser.open('file://' + html_path)
                    else:
                        print("HTML result not generated")
                else:
                    print("No matching results found")
            else:
                print("Unable to extract human contour")
        except Exception as e:
            print(f"Error processing captured image: {e}")
            import traceback
            traceback.print_exc()


class AnimalPoseMatcherServer:
    """Provide Web interface for Animal Pose Matcher"""

    def __init__(self, retrieval_system, port=8080):
        self.port = port
        self.retrieval = retrieval_system
        self.latest_image_path = None
        self.latest_results = None
        self.server = None
        self.server_thread = None
        # 替换OpenAI API密钥为Gemini API密钥
        self.gemini_api_key = "AIzaSyAs1V__qlCjvp4nzoR7LmQ27b9D9wkL4Cg"  # 实际应用中应从环境变量获取
        # 初始化Gemini API
        genai.configure(api_key=self.gemini_api_key)
        # Unsplash API密钥
        self.unsplash_api_key = "*******************************************"  # 实际应用中应从环境变量获取

    # 修改方法以使用Gemini API获取动物信息
    def get_animal_info(self, animal_name):
        """使用Gemini API获取动物的详细信息"""
        try:
            # 配置Gemini模型
            generation_config = {
                "temperature": 0.7,
                "top_p": 0.95,
                "top_k": 64,
                "max_output_tokens": 2048,
            }

            # 创建Gemini模型实例
            model = genai.GenerativeModel(
                model_name="gemini-1.5-pro",
                generation_config=generation_config
            )

            # 构建提示，要求以JSON格式返回特定结构的数据
            prompt = """请提供关于"{0}"的以下信息，并严格按照JSON格式返回：
            1. 保护状况 (conservation_status): 例如"易危"、"濒危"等
            2. 简介/描述 (description): 一段简短的描述，类似"This magnificent animal is part of our museum collection. Visit the exhibit to learn more about its habitat, behavior, and conservation status."
            3. 饮食 (diet): 简短描述其饮食习惯，如"Varies by species"
            4. 栖息地 (habitat): 简短描述其栖息地，如"Natural habitats across Africa"
            5. 寿命 (lifespan): 典型寿命范围，如"15-30 years in the wild"
            6. 体重 (weight): 典型体重范围，如"Varies by species and age"
            7. 有趣的事实 (fun_fact): 一个有趣的事实，如"This animal is part of a critical conservation effort at our museum."

            回复必须仅包含有效的JSON格式，不要有任何前导或尾随文本。示例格式:
            {{
              "conservation_status": "Vulnerable",
              "description": "This magnificent animal...",
              "diet": "Varies by species",
              "habitat": "Natural habitats across Africa",
              "lifespan": "15-30 years in the wild",
              "weight": "Varies by species and age",
              "fun_fact": "This animal is part of a critical conservation effort at our museum."
            }}
            """.format(animal_name)

            # 发送请求到Gemini API
            response = model.generate_content(prompt)

            # 解析响应文本
            response_text = response.text
            print(f"Gemini API 返回: {response_text[:200]}...")  # 打印前200个字符进行调试

            # 尝试提取JSON部分（如果有其他文本包裹着JSON）
            try:
                # 查找JSON的开始和结束位置
                start_idx = response_text.find('{')
                end_idx = response_text.rfind('}') + 1

                if start_idx != -1 and end_idx != -1:
                    json_str = response_text[start_idx:end_idx]
                    animal_info = json.loads(json_str)
                else:
                    # 如果没有找到JSON格式，尝试直接解析全文
                    animal_info = json.loads(response_text)

                # 确保所有必要的字段都存在，如果不存在则使用默认值
                default_info = {
                    "conservation_status": "Vulnerable",
                    "description": "This magnificent animal is part of our museum collection. Visit the exhibit to learn more about its habitat, behavior, and conservation status.",
                    "diet": "Varies by species",
                    "habitat": "Natural habitats across Africa",
                    "lifespan": "15-30 years in the wild",
                    "weight": "Varies by species and age",
                    "fun_fact": "This animal is part of a critical conservation effort at our museum."
                }

                # 合并API返回的数据和默认数据，如果API没有返回某些字段则使用默认值
                for key in default_info:
                    if key not in animal_info or not animal_info[key]:
                        animal_info[key] = default_info[key]

            except json.JSONDecodeError as e:
                print(f"无法解析Gemini API返回的JSON: {e}")
                print(f"原始响应: {response_text}")
                # 使用所有默认值
                animal_info = {
                    "conservation_status": "Vulnerable",
                    "description": "This magnificent animal is part of our museum collection. Visit the exhibit to learn more about its habitat, behavior, and conservation status.",
                    "diet": "Varies by species",
                    "habitat": "Natural habitats across Africa",
                    "lifespan": "15-30 years in the wild",
                    "weight": "Varies by species and age",
                    "fun_fact": "This animal is part of a critical conservation effort at our museum."
                }

            # 构建返回数据结构
            return {
                "success": True,
                "data": animal_info
            }

        except Exception as e:
            print(f"使用Gemini API获取动物信息时出错: {str(e)}")
            # 在出错的情况下也返回默认信息，确保用户体验不受影响
            default_info = {
                "conservation_status": "Vulnerable",
                "description": "This magnificent animal is part of our museum collection. Visit the exhibit to learn more about its habitat, behavior, and conservation status.",
                "diet": "Varies by species",
                "habitat": "Natural habitats across Africa",
                "lifespan": "15-30 years in the wild",
                "weight": "Varies by species and age",
                "fun_fact": "This animal is part of a critical conservation effort at our museum."
            }
            return {
                "success": True,
                "data": default_info,
                "error_message": str(e)
            }

    def start(self):
        """Start HTTP server"""
        # Define custom HTTP request handler
        handler = self.create_request_handler()

        # Create HTTP server
        self.server = socketserver.TCPServer(("", self.port), handler)

        # Start server in a separate thread
        self.server_thread = threading.Thread(target=self.server.serve_forever)
        self.server_thread.daemon = True
        self.server_thread.start()

        print(f"Animal Pose Matcher server started, listening on port {self.port}")
        print(f"Please visit: http://localhost:{self.port}")

        # Open default browser
        webbrowser.open(f"http://localhost:{self.port}")

        # Wait for server thread to exit
        try:
            while True:
                time.sleep(1)
        except KeyboardInterrupt:
            print("Shutting down server...")
            self.server.shutdown()
            self.server_thread.join()

    def create_request_handler(self):
        """Create custom request handler class"""
        retrieval_system = self.retrieval
        server_instance = self

        class RequestHandler(http.server.SimpleHTTPRequestHandler):
            """Custom request handler class"""

            def log_message(self, format, *args):
                """Disable unnecessary logging"""
                pass

            def do_GET(self):
                """Handle GET requests"""
                parsed_path = urllib.parse.urlparse(self.path)
                query_params = urllib.parse.parse_qs(parsed_path.query)

                # Home page - display photo capture interface
                if parsed_path.path == '/' or parsed_path.path == '/index.html':
                    self.send_response(200)
                    self.send_header('Content-type', 'text/html')
                    self.end_headers()

                    # Generate photo capture interface HTML
                    html_content = retrieval_system.create_integrated_html(stage="capture")
                    self.wfile.write(html_content.encode())

                # 处理动物信息API请求
                elif parsed_path.path == '/api/animal-info':
                    try:
                        print(f"收到动物信息API请求: {self.path}")
                        print(f"查询参数: {query_params}")

                        # 解析查询参数
                        if 'name' not in query_params:
                            print("错误: 缺少动物名称参数")
                            self.send_response(400)
                            self.send_header('Content-type', 'application/json')
                            self.send_header('Access-Control-Allow-Origin', '*')  # 允许跨域请求
                            self.end_headers()
                            self.wfile.write(json.dumps({"success": False, "error": "缺少动物名称参数"}).encode())
                            return

                        animal_name = query_params['name'][0]
                        print(f"请求动物信息: {animal_name}")

                        # 获取动物信息
                        animal_info = server_instance.get_animal_info(animal_name)
                        print(f"获取到动物信息: {animal_info}")

                        # 发送响应
                        self.send_response(200)
                        self.send_header('Content-type', 'application/json')
                        self.send_header('Access-Control-Allow-Origin', '*')  # 允许跨域请求
                        self.end_headers()

                        # 将响应数据转换为JSON并发送
                        response_json = json.dumps(animal_info)
                        print(f"发送响应: {response_json[:100]}...")  # 只打印前100个字符
                        self.wfile.write(response_json.encode())

                    except Exception as e:
                        print(f"处理动物信息API请求时出错: {str(e)}")
                        traceback.print_exc()  # 打印详细的错误堆栈
                        self.send_response(500)
                        self.send_header('Content-type', 'application/json')
                        self.send_header('Access-Control-Allow-Origin', '*')  # 允许跨域请求
                        self.end_headers()
                        self.wfile.write(json.dumps({
                            "success": False,
                            "error": f"获取动物信息时出错: {str(e)}"
                        }).encode())
                    return

                # Take Photo page
                elif parsed_path.path == '/take-photo.html':
                    self.send_response(200)
                    self.send_header('Content-type', 'text/html')
                    self.end_headers()

                    # Generate the original camera capture interface HTML
                    html_content = """<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Take Photo - Animal Pose Matcher</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
        }

        body {
            width: 100vw;
            height: 100vh;
            overflow: hidden;
            background-color: #000;
        }

        .camera-container {
            width: 100%;
            height: 100%;
            position: relative;
            overflow: hidden;
            background-color: #000;
        }

        .camera-feed {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .header-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            padding: 20px;
            background: linear-gradient(to bottom, rgba(0,0,0,0.7) 0%, rgba(0,0,0,0) 100%);
            color: white;
            z-index: 10;
        }

        .header-overlay h1 {
            font-size: 28px;
            font-weight: bold;
            margin-bottom: 5px;
        }

        .header-overlay p {
            font-size: 16px;
            opacity: 0.8;
        }

        .footer-controls {
            position: absolute;
            bottom: 30px;
            left: 0;
            width: 100%;
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 20px;
            z-index: 10;
        }

        .btn {
            background-color: rgba(0,0,0,0.7);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 50px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
            backdrop-filter: blur(5px);
            border: 1px solid rgba(255,255,255,0.3);
        }

        .btn-back {
            padding-left: 25px;
            padding-right: 30px;
        }

        .btn-back::before {
            content: "←";
            margin-right: 8px;
        }

        .btn-capture {
            padding-right: 25px;
            padding-left: 30px;
            display: flex;
            align-items: center;
        }

        .btn-capture::before {
            content: "📷";
            margin-right: 8px;
            font-size: 22px;
        }

        .countdown {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            font-size: 180px;
            font-weight: bold;
            color: white;
            background-color: rgba(0,0,0,0.7);
            width: 200px;
            height: 200px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            opacity: 0;
            z-index: 20;
            transition: opacity 0.3s;
        }

        .countdown.visible {
            opacity: 1;
        }

        .countdown-text {
            display: none;
        }

        .countdown-text.visible {
            display: block;
        }
    </style>
</head>
<body>
    <div class="camera-container">
        <video id="camera" class="camera-feed" autoplay></video>
        <canvas id="captureCanvas" style="display:none;"></canvas>
        <canvas id="overlayCanvas" style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; z-index: 5; pointer-events: none;"></canvas>

        <div class="header-overlay">
            <h1>Strike Your Best Animal Pose!</h1>
            <p>Try to mimic a characteristic pose of an animal</p>
        </div>

        <div class="countdown" id="countdown">3</div>

        <div class="footer-controls">
            <button class="btn btn-back" id="backBtn">Back</button>
            <button class="btn btn-capture" id="captureBtn">Capture Pose</button>
        </div>
        <div class="countdown-text" id="countdownText" style="position: absolute; bottom: 100px; width: 100%; text-align: center; color: white; font-size: 20px; z-index: 10;">Capturing in 1...</div>
    </div>

    <script>
        // Get elements
        const video = document.getElementById('camera');
        const canvas = document.getElementById('captureCanvas');
        const overlayCanvas = document.getElementById('overlayCanvas');
        const captureBtn = document.getElementById('captureBtn');
        const backBtn = document.getElementById('backBtn');
        const countdown = document.getElementById('countdown');
        const countdownText = document.getElementById('countdownText');
        let mediaStream = null;
        let realtimeInterval = null;

        // Access camera
        async function startCamera() {
            try {
                mediaStream = await navigator.mediaDevices.getUserMedia({
                    video: {
                        width: { ideal: 1280 },
                        height: { ideal: 720 }
                    }
                });
                video.srcObject = mediaStream;
            } catch (err) {
                console.error('Error accessing camera:', err);
                alert('Could not access camera. Please ensure you have a camera connected and have granted permission.');
            }
        }

        // Start camera
        startCamera();

        // Capture image and send to server
        function captureAndSend() {
            // Set canvas size to match video
            canvas.width = video.videoWidth;
            canvas.height = video.videoHeight;

            // Draw current video frame on canvas
            const ctx = canvas.getContext('2d');
            ctx.drawImage(video, 0, 0, canvas.width, canvas.height);

            // Stop camera
            if (mediaStream) {
                mediaStream.getTracks().forEach(track => track.stop());
            }

            // Convert Canvas to Base64 encoded image
            const imageData = canvas.toDataURL('image/jpeg');

            // Send image data to server
            fetch('/capture', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    image: imageData
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // 直接跳转到结果页面
                    window.location.href = '/result.html';
                } else {
                    alert('Error processing image: ' + (data.error || 'Unknown error'));
                    window.location.reload(); // Reload page to start over
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('Error sending image to server. Please try again.');
                window.location.reload();
            });
        }

        // Setup realtime contour detection
        function startRealtimeContourDetection() {
            // Setup canvas - 修复轮廓显示问题
            function updateCanvasSize() {
                const videoRect = video.getBoundingClientRect();
                overlayCanvas.width = videoRect.width;
                overlayCanvas.height = videoRect.height;
                console.log(`Canvas size updated: ${overlayCanvas.width}x${overlayCanvas.height}`);
            }

            // 确保视频加载完成后立即更新画布尺寸
            if (video.readyState >= 2) {
                updateCanvasSize();
            } else {
                video.onloadeddata = updateCanvasSize;
            }

            // Listen for video size changes
            new ResizeObserver(updateCanvasSize).observe(video);

            // Start realtime detection
            realtimeInterval = setInterval(() => {
                if (!video.paused && !video.ended) {
                    const videoRect = video.getBoundingClientRect();

                    // --- 修改开始: 使用视频的原始尺寸而不是显示尺寸 ---
                    const tempCanvas = document.createElement('canvas');
                    tempCanvas.width = video.videoWidth;
                    tempCanvas.height = video.videoHeight;

                    const ctx = tempCanvas.getContext('2d');
                    ctx.drawImage(video, 0, 0, video.videoWidth, video.videoHeight);
                    // --- 修改结束 ---

                    const imageData = tempCanvas.toDataURL('image/jpeg', 0.7);

                    fetch('/contour-only', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            image: imageData
                        })
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success && data.contour) {
                            // --- 修改: 移除冗余调用 ---
                            // updateCanvasSize();

                            // --- 添加调试日志 - 验证前端和后端尺寸是否匹配 ---
                            console.log(`前端视频原始尺寸: ${video.videoWidth}x${video.videoHeight}`);
                            console.log(`后端处理图像尺寸: ${data.image_width}x${data.image_height}`);
                            if (video.videoWidth !== data.image_width || video.videoHeight !== data.image_height) {
                                console.warn('警告: 前端视频尺寸与后端处理尺寸不匹配!');
                            }

                            const overlayCtx = overlayCanvas.getContext('2d');
                            overlayCtx.clearRect(0, 0, overlayCanvas.width, overlayCanvas.height);

                            const contour = JSON.parse(data.contour);

                            overlayCtx.strokeStyle = 'green';
                            overlayCtx.lineWidth = 3;
                            overlayCtx.beginPath();

                            if (contour.length > 0) {
                                const originalWidth = data.image_width;
                                const originalHeight = data.image_height;
                                const bbox = data.bbox || [0, 0, originalWidth, originalHeight];

                                const scaleX = overlayCanvas.width / originalWidth;
                                const scaleY = overlayCanvas.height / originalHeight;

                                for (let i = 0; i < contour.length; i++) {
                                    const point = contour[i][0];
                                    
                                    // 简单直接的坐标变换
                                    const x = point[0] * scaleX;
                                    const y = point[1] * scaleY;
                                    
                                    if (i === 0) {
                                        overlayCtx.moveTo(x, y);
                                    } else {
                                        overlayCtx.lineTo(x, y);
                                    }
                                }

                                overlayCtx.closePath();
                                overlayCtx.stroke();
                            }
                        }
                    })
                    .catch(error => {
                        console.error('Error getting contour:', error);
                    });
                }
            }, 200);
        }

        // Start realtime contour detection immediately
        startRealtimeContourDetection();

        // Countdown photo
        captureBtn.addEventListener('click', () => {
            captureBtn.style.display = 'none';
            countdownText.classList.add('visible');
            countdown.classList.add('visible');

            let count = 3;
            countdown.textContent = count;
            countdownText.textContent = `Capturing in ${count}...`;

            const countdownInterval = setInterval(() => {
                count--;
                countdown.textContent = count;
                countdownText.textContent = `Capturing in ${count}...`;

                if (count <= 0) {
                    clearInterval(countdownInterval);
                    setTimeout(() => {
                        countdown.textContent = '📸';
                        countdownText.textContent = 'CHEESE!';
                        setTimeout(() => {
                            countdown.classList.remove('visible');
                            countdownText.classList.remove('visible');
                            // Capture image and send to server
                            captureAndSend();
                        }, 500);
                    }, 1000);
                }
            }, 1000);
        });

        // Back button
        backBtn.addEventListener('click', () => {
            if (mediaStream) {
                mediaStream.getTracks().forEach(track => track.stop());
            }

            if (realtimeInterval) {
                clearInterval(realtimeInterval);
            }

            window.location.href = '/';
        });
    </script>
"""
                    self.wfile.write(html_content.encode())

                # Processing page
                elif parsed_path.path == '/processing.html':
                    self.send_response(200)
                    self.send_header('Content-type', 'text/html')
                    self.end_headers()

                    # Generate processing interface HTML
                    html_content = retrieval_system.create_integrated_html(stage="processing")
                    self.wfile.write(html_content.encode())

                # Result page
                elif parsed_path.path == '/result.html':
                    self.send_response(200)
                    self.send_header('Content-type', 'text/html')
                    self.end_headers()

                    # Ensure there are results to display
                    if server_instance.latest_results and server_instance.latest_image_path:
                        # Check if result images exist
                        human_img_path = retrieval_system.results_dir / "human_pose.jpg"
                        animal_img_path = retrieval_system.results_dir / "animal_pose.jpg"

                        if not os.path.exists(human_img_path) or not os.path.exists(animal_img_path):
                            print(f"Generating result images...")
                            # Regenerate images
                            retrieval_system._save_contour_images(
                                server_instance.latest_image_path,
                                human_contour=retrieval_system.extract_human_contour(server_instance.latest_image_path),
                                result=server_instance.latest_results[0]
                            )

                        # Generate result interface HTML
                        html_content = retrieval_system.create_integrated_html(
                            image_path=server_instance.latest_image_path,
                            results=server_instance.latest_results,
                            stage="result"
                        )
                        self.wfile.write(html_content.encode())
                    else:
                        # If no results, redirect to home page
                        self.send_response(302)
                        self.send_header('Location', '/')
                        self.end_headers()

                # Provide image files
                elif parsed_path.path.startswith('/images/'):
                    image_name = parsed_path.path.split('/')[-1]

                    # Check for homepage slideshow images
                    if image_name.startswith('homepage_'):
                        # Map to the actual files in the homepage pic folder
                        homepage_pic_dir = Path('/Users/<USER>/Desktop/homepage pic')

                        # Map index numbers to actual files
                        homepage_files = list(homepage_pic_dir.glob('*.jp*g'))
                        homepage_files.extend(list(homepage_pic_dir.glob('*.JP*G')))

                        if not homepage_files:
                            self.send_error(404, f"No homepage images found")
                            return

                        # Get the index from the requested filename (homepage_1.jpg -> 1)
                        try:
                            index = int(image_name.split('_')[1].split('.')[0]) - 1
                            if index < 0:
                                index = 0
                            if index >= len(homepage_files):
                                index = len(homepage_files) - 1

                            image_path = homepage_files[index]
                        except:
                            # Default to first image if parsing fails
                            image_path = homepage_files[0]
                    elif image_name in ["human_pose.jpg", "animal_pose.jpg"]:
                        # 确保结果图像路径是绝对路径
                        image_path = retrieval_system.results_dir / image_name
                        print(f"Result image request: {image_name}, path: {image_path}, exists: {os.path.exists(image_path)}")

                        # 如果图像不存在，尝试从服务器实例的最新结果重新生成
                        if not os.path.exists(image_path) and server_instance.latest_results and server_instance.latest_image_path:
                            print(f"Image {image_path} doesn't exist, trying to regenerate...")
                            try:
                                human_contour = retrieval_system.extract_human_contour(server_instance.latest_image_path)
                                retrieval_system._save_contour_images(
                                    server_instance.latest_image_path,
                                    human_contour=human_contour,
                                    result=server_instance.latest_results[0]
                                )
                                print(f"Regenerated images, now exists: {os.path.exists(image_path)}")
                            except Exception as e:
                                print(f"Failed to regenerate images: {e}")
                    else:
                        # Regular image request
                        image_path = retrieval_system.results_dir / image_name

                    print(f"Processing image request: {parsed_path.path}")
                    print(f"Looking for image file: {image_path}")

                    if os.path.exists(image_path):
                        print(f"Found image file: {image_path}, size: {os.path.getsize(image_path)} bytes")
                        self.send_response(200)

                        # Determine content type based on extension
                        if str(image_path).lower().endswith(('.jpg', '.jpeg')):
                            self.send_header('Content-type', 'image/jpeg')
                        elif str(image_path).lower().endswith('.png'):
                            self.send_header('Content-type', 'image/png')
                        else:
                            self.send_header('Content-type', 'image/jpeg')  # Default

                        self.end_headers()

                        try:
                            with open(image_path, 'rb') as f:
                                image_data = f.read()
                                self.wfile.write(image_data)
                                print(f"Image data sent: {len(image_data)} bytes")
                        except Exception as e:
                            print(f"Failed to read image file: {e}")
                            self.send_error(500, f"Error reading image: {e}")
                    else:
                        print(f"Image file doesn't exist: {image_path}")
                        # 如果是结果图像但不存在，返回错误信息而不是404
                        if image_name in ["human_pose.jpg", "animal_pose.jpg"]:
                            self.send_response(200)
                            self.send_header('Content-type', 'image/jpeg')
                            self.end_headers()

                            # 创建一个带有错误文本的简单图像
                            blank_img = np.ones((300, 400, 3), dtype=np.uint8) * 255
                            cv2.putText(blank_img, f"Image not found", (50, 150), cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 0, 255), 2)

                            # 转换为JPEG并发送
                            _, img_encoded = cv2.imencode('.jpg', blank_img)
                            self.wfile.write(img_encoded.tobytes())
                            print(f"Sent placeholder image for missing {image_name}")
                        else:
                            self.send_error(404, f"Image not found: {image_name}")

                # Check result status
                elif parsed_path.path == '/check-results':
                    self.send_response(200)
                    self.send_header('Content-type', 'application/json')
                    self.end_headers()

                    # Check if results are available
                    ready = (server_instance.latest_results is not None and
                           server_instance.latest_image_path is not None)

                    self.wfile.write(json.dumps({
                        'ready': ready
                    }).encode())

                # Handle sending result to email request
                elif parsed_path.path == '/send-result-preview':
                    self.send_response(200)
                    self.send_header('Content-type', 'image/jpeg')
                    self.end_headers()

                    # Send result preview image
                    if server_instance.latest_results:
                        # Create merged preview image, here simply return result image
                        preview_path = retrieval_system.results_dir / "human_pose.jpg"
                        if os.path.exists(preview_path):
                            with open(preview_path, 'rb') as f:
                                self.wfile.write(f.read())
                        else:
                            self.send_error(404, "Preview image not found")
                    else:
                        self.send_error(404, "No results available")

                # 添加新的API端点 - 获取动物信息
                elif parsed_path.path == '/api/animal-info':
                    try:
                        print(f"收到动物信息API请求: {self.path}")
                        print(f"查询参数: {query_params}")

                        # 解析查询参数
                        if 'name' not in query_params:
                            print("错误: 缺少动物名称参数")
                            self.send_response(400)
                            self.send_header('Content-type', 'application/json')
                            self.send_header('Access-Control-Allow-Origin', '*')  # 允许跨域请求
                            self.end_headers()
                            self.wfile.write(json.dumps({"success": False, "error": "缺少动物名称参数"}).encode())
                            return

                        animal_name = query_params['name'][0]
                        print(f"请求动物信息: {animal_name}")

                        # 获取动物信息
                        animal_info = server_instance.get_animal_info(animal_name)
                        print(f"获取到动物信息: {animal_info}")

                        # 发送响应
                        self.send_response(200)
                        self.send_header('Content-type', 'application/json')
                        self.send_header('Access-Control-Allow-Origin', '*')  # 允许跨域请求
                        self.end_headers()

                        # 将响应数据转换为JSON并发送
                        response_json = json.dumps(animal_info)
                        print(f"发送响应: {response_json[:100]}...")  # 只打印前100个字符
                        self.wfile.write(response_json.encode())

                    except Exception as e:
                        print(f"处理动物信息API请求时出错: {str(e)}")
                        traceback.print_exc()  # 打印详细的错误堆栈
                        self.send_response(500)
                        self.send_header('Content-type', 'application/json')
                        self.send_header('Access-Control-Allow-Origin', '*')  # 允许跨域请求
                        self.end_headers()
                        self.wfile.write(json.dumps({
                            "success": False,
                            "error": f"获取动物信息时出错: {str(e)}"
                        }).encode())
                    return

                # 添加新的API端点 - 获取Unsplash图片
                elif parsed_path.path == '/api/unsplash-images':
                    try:
                        print(f"收到Unsplash图片API请求: {self.path}")
                        print(f"查询参数: {query_params}")

                        # 解析查询参数
                        if 'name' not in query_params:
                            print("错误: 缺少动物名称参数")
                            self.send_response(400)
                            self.send_header('Content-type', 'application/json')
                            self.send_header('Access-Control-Allow-Origin', '*')  # 允许跨域请求
                            self.end_headers()
                            self.wfile.write(json.dumps({"success": False, "error": "缺少动物名称参数"}).encode())
                            return

                        animal_name = query_params['name'][0]
                        print(f"请求Unsplash图片: {animal_name}")

                        # 获取Unsplash图片
                        unsplash_images = server_instance.get_unsplash_images(animal_name)
                        print(f"获取到Unsplash图片信息: {len(unsplash_images.get('images', [])) if unsplash_images.get('success') else 'Error'}")

                        # 发送响应
                        self.send_response(200)
                        self.send_header('Content-type', 'application/json')
                        self.send_header('Access-Control-Allow-Origin', '*')  # 允许跨域请求
                        self.end_headers()

                        # 将响应数据转换为JSON并发送
                        response_json = json.dumps(unsplash_images)
                        self.wfile.write(response_json.encode())
                    except Exception as e:
                        print(f"处理Unsplash图片API请求时出错: {str(e)}")
                        traceback.print_exc()  # 打印详细的错误堆栈
                        self.send_response(500)
                        self.send_header('Content-type', 'application/json')
                        self.send_header('Access-Control-Allow-Origin', '*')  # 允许跨域请求
                        self.end_headers()
                        self.wfile.write(json.dumps({
                            "success": False,
                            "error": f"获取Unsplash图片时出错: {str(e)}"
                        }).encode())
                    return

                else:
                    self.send_error(404, "Page not found")

            def do_POST(self):
                """Handle POST requests"""
                if self.path == '/contour-only':
                    # Handle realtime outline recognition request
                    content_length = int(self.headers['Content-Length'])
                    post_data = self.rfile.read(content_length)
                    data = json.loads(post_data.decode('utf-8'))

                    # Create image from Base64 encoded image data
                    if 'image' in data:
                        try:
                            # Decode Base64 image
                            img_data = base64.b64decode(data['image'].split(',')[1])

                            # Generate temporary filename
                            temp_filename = f"temp_frame_{uuid.uuid4()}.jpg"
                            temp_path = retrieval_system.results_dir / temp_filename

                            # Save temporary image
                            with open(temp_path, 'wb') as f:
                                f.write(img_data)

                            # Extract human contour but do not perform retrieval
                            try:
                                # Use YOLO model to detect human
                                results = retrieval_system.human_model(temp_path, verbose=False)

                                # Get segmentation mask
                                if results and results[0].masks:
                                    # Get human mask
                                    person_masks = []
                                    for r in results[0].boxes.data:
                                        if int(r[5]) == 0:  # 0 represents COCO dataset person
                                            mask_idx = int(r[4])  # Get corresponding mask index
                                            if mask_idx < len(results[0].masks):
                                                person_masks.append((float(r[4]), results[0].masks[mask_idx].data[0].cpu().numpy()))

                                    if person_masks:
                                        # Use mask with highest confidence
                                        mask = max(person_masks, key=lambda x: x[0])[1]

                                        # Extract contour
                                        mask = (mask * 255).astype(np.uint8)
                                        contours, _ = cv2.findContours(mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

                                        if contours:
                                            # Get largest contour
                                            max_contour = max(contours, key=cv2.contourArea)

                                            # Read original image to get size
                                            img = cv2.imread(str(temp_path))
                                            img_h, img_w = img.shape[:2]

                                            # Get contour bounding box
                                            x, y, w, h = cv2.boundingRect(max_contour)

                                            # Use original contour directly
                                            contour_json = max_contour.tolist()

                                            # Send success response
                                            self.send_response(200)
                                            self.send_header('Content-type', 'application/json')
                                            self.end_headers()
                                            self.wfile.write(json.dumps({
                                                'success': True,
                                                'contour': json.dumps(contour_json),
                                                'image_width': img_w,
                                                'image_height': img_h,
                                                'bbox': [x, y, w, h]  # Send bounding box information
                                            }).encode())

                                            # Delete temporary file
                                            os.remove(temp_path)
                                            return

                                # If no human detected, return empty data
                                self.send_response(200)
                                self.send_header('Content-type', 'application/json')
                                self.end_headers()
                                self.wfile.write(json.dumps({
                                    'success': False,
                                    'error': 'No human detected'
                                }).encode())

                            except Exception as e:
                                # Handle error
                                self.send_response(500)
                                self.send_header('Content-type', 'application/json')
                                self.end_headers()
                                self.wfile.write(json.dumps({
                                    'success': False,
                                    'error': str(e)
                                }).encode())

                            # Delete temporary file (if still exists)
                            if os.path.exists(temp_path):
                                os.remove(temp_path)

                        except Exception as e:
                            # Handle error
                            self.send_response(500)
                            self.send_header('Content-type', 'application/json')
                            self.end_headers()
                            self.wfile.write(json.dumps({
                                'success': False,
                                'error': str(e)
                            }).encode())
                    else:
                        # Missing image data
                        self.send_response(400)
                        self.send_header('Content-type', 'application/json')
                        self.end_headers()
                        self.wfile.write(json.dumps({
                            'success': False,
                            'error': 'No image data provided'
                        }).encode())

                elif self.path == '/capture':
                    content_length = int(self.headers['Content-Length'])
                    post_data = self.rfile.read(content_length)
                    data = json.loads(post_data.decode('utf-8'))

                    # Create image from Base64 encoded image data
                    if 'image' in data:
                        try:
                            # Decode Base64 image
                            img_data = base64.b64decode(data['image'].split(',')[1])

                            # Generate unique timestamp filename
                            timestamp = int(time.time())
                            img_filename = f"web_capture_{timestamp}.jpg"
                            img_path = retrieval_system.results_dir / img_filename

                            # Save image
                            with open(img_path, 'wb') as f:
                                f.write(img_data)

                            print(f"Web capture saved to {img_path}")

                            # 清理旧的捕获图像
                            for old_file in retrieval_system.results_dir.glob("web_capture_*.jpg"):
                                if old_file != img_path and os.path.exists(old_file):
                                    try:
                                        os.remove(old_file)
                                    except:
                                        pass

                            # Process image and retrieve similar poses
                            try:
                                # Extract human contour
                                human_contour = retrieval_system.extract_human_contour(img_path)

                                if human_contour is not None:
                                    # Retrieve similar poses
                                    results = retrieval_system.retrieve(human_contour, top_k=1)

                                    # Debug output for results
                                    if results and len(results) > 0:
                                        result = results[0]
                                        print(f"Retrieved result: category='{result.get('category')}', "
                                            f"image_id='{result.get('image_id')}', "
                                            f"index={result.get('index')}, "
                                            f"similarity={result.get('similarity_score', 0) * 100:.1f}%")

                                    # Save results for display
                                    server_instance.latest_image_path = img_path
                                    server_instance.latest_results = results

                                    if results and len(results) > 0:
                                        # Ensure result images are generated
                                        human_img_path, animal_img_path = retrieval_system._save_contour_images(
                                            img_path,
                                            human_contour=human_contour,
                                            result=results[0]
                                        )

                                        # Check if images were generated
                                        if human_img_path and animal_img_path and os.path.exists(human_img_path) and os.path.exists(animal_img_path):
                                            print(f"Result images generated: {human_img_path}, {animal_img_path}")

                                            # Send success response
                                            self.send_response(200)
                                            self.send_header('Content-type', 'application/json')
                                            self.end_headers()
                                            self.wfile.write(json.dumps({
                                                'success': True,
                                                'redirect': '/result.html'
                                            }).encode())
                                            return
                                        else:
                                            error_message = "Failed to generate result images"
                                    else:
                                        error_message = "No matching results found"
                                else:
                                    error_message = "No human detected in image"

                                # If we reach here, something went wrong
                                self.send_response(400)
                                self.send_header('Content-type', 'application/json')
                                self.end_headers()
                                self.wfile.write(json.dumps({
                                    'success': False,
                                    'error': error_message
                                }).encode())

                            except Exception as e:
                                # Handle error
                                import traceback
                                traceback.print_exc()
                                self.send_response(500)
                                self.send_header('Content-type', 'application/json')
                                self.end_headers()
                                self.wfile.write(json.dumps({
                                    'success': False,
                                    'error': str(e)
                                }).encode())
                        except Exception as e:
                            import traceback
                            traceback.print_exc()
                            # Handle error
                            self.send_response(500)
                            self.send_header('Content-type', 'application/json')
                            self.end_headers()
                            self.wfile.write(json.dumps({
                                'success': False,
                                'error': f"Error processing image: {str(e)}"
                            }).encode())
                    else:
                        # Missing image data
                        self.send_response(400)
                        self.send_header('Content-type', 'application/json')
                        self.end_headers()
                        self.wfile.write(json.dumps({
                            'success': False,
                            'error': 'No image data provided'
                        }).encode())
                elif self.path == '/send-result':
                    content_length = int(self.headers['Content-Length'])
                    post_data = self.rfile.read(content_length)
                    data = json.loads(post_data.decode('utf-8'))

                    # Handle sending email request
                    if 'email' in data:
                        email = data['email']

                        # Here simply simulate email sending success, actual application needs to integrate real email sending service
                        # Like SMTP or third-party service
                        print(f"Received email sending request: {email}")
                        print(f"Animal category: {data.get('animalCategory', 'Unknown')}")
                        print(f"Match score: {data.get('matchScore', 'Unknown')}")

                        # Return success response
                        self.send_response(200)
                        self.send_header('Content-type', 'application/json')
                        self.end_headers()

                        # In production environment, this should send real email
                        self.wfile.write(json.dumps({
                            'success': True,
                            'message': f'Result has been sent to your email!'
                        }).encode())
                    else:
                        # Missing email address
                        self.send_response(400)
                        self.send_header('Content-type', 'application/json')
                        self.end_headers()
                        self.wfile.write(json.dumps({
                            'success': False,
                            'error': 'Email address is required'
                        }).encode())
                else:
                    self.send_error(404, "Endpoint not found")

        return RequestHandler

    def stop(self):
        """Stop HTTP server"""
        if self.server:
            self.server.shutdown()
            if self.server_thread:
                self.server_thread.join()
            print("Server stopped")



def main():
    """Main function, start Animal Pose Matcher Web application"""
    print("Animal Pose Matcher system starting...")

    # Set up base directory
    base_dir = Path('/Users/<USER>/Desktop/pose_matching_project')
    # 修改为直接在桌面创建results目录，确保更容易访问
    results_dir = Path('/Users/<USER>/Desktop/results')
    # 确保结果目录存在
    results_dir.mkdir(exist_ok=True)
    print(f"Results directory: {results_dir} (exists: {results_dir.exists()})")

    # Model and database paths
    human_model_path = base_dir / "models" / "human_seg_model.pt"
    animal_db_path = base_dir / "database" / "animal_contours.pkl"

    # Parameter parsing
    parser = argparse.ArgumentParser(description='Animal Pose Matcher')
    parser.add_argument('--port', type=int, default=8080, help='Web server port')
    parser.add_argument('--mode', choices=['web', 'cv'], default='web', help='Run mode: web (browser interface) or cv (OpenCV interface)')
    args = parser.parse_args()

    try:
        # Initialize contour retrieval system
        retrieval_system = ContourRetrieval(
            base_dir=base_dir,
            results_dir=results_dir,
            human_model_path=human_model_path,
            animal_db_path=animal_db_path
        )

        # Choose startup method based on running mode
        if args.mode == 'web':
            # Start Web server
            server = AnimalPoseMatcherServer(
                retrieval_system=retrieval_system,
                port=args.port
            )
            server.start()
        else:
            # Use OpenCV interface
            camera_system = CameraRetrieval(retrieval_system)
            camera_system.capture_and_retrieve()

    except Exception as e:
        print(f"Error: {e}")
        traceback.print_exc()
        return 1

    return 0


if __name__ == "__main__":
    import argparse
    import sys
    import traceback
    sys.exit(main())