# Animal Pose Matcher - 模块化重构版本

这是一个已经完全模块化重构的动物姿态匹配应用程序。原本5000多行的单一文件已被拆分为15个专门的模块，每个模块都有明确的职责，便于维护和扩展。

## 📁 项目结构

```
animal_pose_matcher/
├── main.py                    # 主应用入口
├── config.py                  # 配置管理
├── model_manager.py           # 模型管理
├── database_manager.py        # 数据库管理
├── contour_utils.py          # 轮廓处理工具
├── human_processor.py        # 人体处理器
├── animal_processor.py       # 动物处理器
├── pose_matching_engine.py   # 姿态匹配引擎
├── external_apis_manager.py  # 外部API管理
├── result_visualizer.py      # 结果可视化（待实现）
├── html_templates.py         # HTML模板生成（待实现）
├── api_handler.py            # API请求处理（待实现）
├── server_manager.py         # 服务器管理（待实现）
├── opencv_app.py             # OpenCV应用（待实现）
└── README.md                 # 项目文档
```

## 🔧 模块功能

### 核心后端模块

1. **config.py** - 配置管理
   - 存储所有全局配置常量
   - 文件路径、API密钥、服务器设置
   - 错误消息和成功消息定义

2. **model_manager.py** - 模型管理
   - 加载和管理YOLO模型
   - 人体分割和动物识别模型
   - 模型验证和错误处理

3. **database_manager.py** - 数据库管理
   - 加载动物轮廓数据库
   - 构建KDTree索引用于快速搜索
   - 数据库验证和统计信息

4. **contour_utils.py** - 轮廓处理工具
   - 计算形状特征（Hu矩、圆度、长宽比等）
   - 轮廓标准化和几何处理
   - 相似度计算函数

5. **human_processor.py** - 人体处理器
   - 使用YOLO进行人体分割
   - 提取和处理人体轮廓
   - 实时摄像头处理

6. **animal_processor.py** - 动物处理器
   - 从JSON标注文件处理动物数据
   - 动物类型检测和掩码提取
   - 动物图像验证

7. **pose_matching_engine.py** - 姿态匹配引擎
   - 核心相似性搜索算法
   - 基于KDTree的快速匹配
   - 过滤和统计功能

8. **external_apis_manager.py** - 外部API管理
   - Google Gemini API集成
   - 动物信息获取
   - API错误处理和回退机制

### 待实现模块

9. **result_visualizer.py** - 结果可视化
10. **html_templates.py** - HTML模板生成
11. **api_handler.py** - API请求处理
12. **server_manager.py** - 服务器管理
13. **opencv_app.py** - OpenCV应用

## 🚀 快速开始

### 安装依赖

```bash
pip install ultralytics opencv-python numpy scipy matplotlib google-generativeai
```

### 基本使用

```bash
# 运行系统测试
python main.py --test

# 启动Web服务器模式
python main.py --mode web --port 8080

# 启动OpenCV摄像头模式
python main.py --mode cv

# 使用自定义模型路径
python main.py --human-model path/to/model.pt --db-path path/to/database.pkl

# 详细输出模式
python main.py --verbose --test
```

### 命令行参数

- `--mode` : 运行模式 (web/cv)
- `--port` : Web服务器端口
- `--human-model` : 人体分割模型路径
- `--animal-model` : 动物识别模型路径（可选）
- `--db-path` : 动物轮廓数据库路径
- `--api-key` : Google Gemini API密钥
- `--results-dir` : 结果保存目录
- `--verbose` : 详细输出
- `--test` : 运行系统测试

## 📚 模块使用示例

### 使用人体处理器

```python
from human_processor import extract_human_contour

# 提取人体轮廓
contour = extract_human_contour("path/to/human_image.jpg")
print(f"轮廓形状: {contour.shape}")
```

### 使用姿态匹配引擎

```python
from pose_matching_engine import match_pose

# 查找相似姿态
results = match_pose(human_contour, top_k=5)
for result in results:
    print(f"匹配: {result['category']}, 相似度: {result['similarity_score']}")
```

### 使用外部API管理器

```python
from external_apis_manager import get_animal_info

# 获取动物信息
info = get_animal_info("长颈鹿")
print(f"保护状态: {info['conservation_status']}")
print(f"描述: {info['description']}")
```

## 🔧 配置说明

在 `config.py` 中修改以下设置：

```python
# 基础目录
BASE_DIR = Path('/your/project/path')
RESULTS_DIR = Path('/your/results/path')

# 模型路径
HUMAN_MODEL_PATH = BASE_DIR / 'models' / 'human_seg_model.pt'
ANIMAL_DB_PATH = BASE_DIR / 'database' / 'animal_contours.pkl'

# API密钥
GEMINI_API_KEY = "your_api_key_here"
```

## 🧪 测试系统

运行全面的系统测试：

```bash
python main.py --test --verbose
```

测试包括：
- 模型管理器功能
- 数据库加载和索引
- 姿态匹配引擎
- 外部API连接

## 🐛 故障排除

### 常见问题

1. **模型文件不存在**
   - 确保 `human_seg_model.pt` 在正确位置
   - 使用 `--human-model` 参数指定路径

2. **数据库文件不存在**
   - 确保 `animal_contours.pkl` 在正确位置
   - 使用 `--db-path` 参数指定路径

3. **API连接失败**
   - 检查网络连接
   - 验证Gemini API密钥
   - 使用 `--api-key` 参数提供密钥

### 日志和调试

使用 `--verbose` 参数获得详细输出：

```bash
python main.py --verbose --test
```

## 📈 性能优化

每个模块都设计为可独立优化：

- **模型管理**: 支持模型缓存和延迟加载
- **数据库**: KDTree索引提供O(log n)查询时间
- **轮廓处理**: 向量化操作提高计算效率
- **API管理**: 智能回退和错误处理

## 🛠️ 扩展开发

要添加新功能：

1. 在相应模块中添加新函数
2. 更新配置文件中的常量
3. 在主应用中集成新功能
4. 添加相应的测试

### 模块间依赖关系

```
main.py
├── config.py
├── model_manager.py
├── database_manager.py
│   └── contour_utils.py
├── human_processor.py
│   ├── model_manager.py
│   └── contour_utils.py
├── animal_processor.py
│   └── model_manager.py
├── pose_matching_engine.py
│   ├── database_manager.py
│   └── contour_utils.py
└── external_apis_manager.py
    └── config.py
```

## 📝 注意事项

1. **模型文件**: 需要预训练的YOLO模型文件
2. **数据库**: 需要预处理的动物轮廓数据库
3. **API密钥**: Gemini API需要有效的API密钥
4. **依赖项**: 确保所有Python包都已正确安装

## 🎯 下一步开发

完整的Web和OpenCV模块还需要实现：

1. `server_manager.py` - HTTP服务器
2. `api_handler.py` - 请求路由和处理
3. `html_templates.py` - 动态HTML生成
4. `opencv_app.py` - 摄像头界面
5. `result_visualizer.py` - 图像和图表生成

这个模块化结构为将来的扩展和维护提供了坚实的基础。 