#!/usr/bin/env python3
"""
Pose Matching Engine module for Animal Pose Matcher

Implements the core pose matching algorithm using KDTree similarity search.
"""

import numpy as np
from config import DEFAULT_SIMILARITY_TOP_K
from contour_utils import compute_shape_features, calculate_similarity_score
from database_manager import get_database_manager


class PoseMatchingEngine:
    """Core pose matching engine using KDTree similarity search"""
    
    def __init__(self, database_manager=None):
        self.db_manager = database_manager or get_database_manager()
        if not self.db_manager.is_loaded() or not self.db_manager.is_indexed():
            raise Exception("Database must be loaded and indexed before creating matcher")
    
    def match_pose(self, human_contour, top_k=DEFAULT_SIMILARITY_TOP_K):
        """
        Find similar poses in the database
        
        Args:
            human_contour: Human contour to match
            top_k: Number of top matches to return
            
        Returns:
            list: List of match results with metadata
        """
        print("Calculating features for human contour...")
        human_features = compute_shape_features(human_contour)
        
        print("Searching for similar poses...")
        try:
            kdtree = self.db_manager.get_kdtree()
            distances, indices = kdtree.query(human_features, k=top_k)
            
            print(f"Raw KDTree query results - distances: {distances}, indices: {indices}")
        except Exception as e:
            print(f"Error during KDTree query: {e}")
            return []
        
        # Ensure results are iterable arrays
        distances = np.atleast_1d(distances)
        indices = np.atleast_1d(indices)
        
        print(f"Processed KDTree results - distances: {distances}, indices: {indices}")
        
        # Validate result lengths
        if len(distances) != len(indices):
            print(f"ERROR: Mismatch in lengths between distances ({len(distances)}) and indices ({len(indices)})")
            return []
        
        return self._process_match_results(distances, indices)
    
    def _process_match_results(self, distances, indices):
        """
        Process KDTree results into structured match results
        
        Args:
            distances: Distance values from KDTree query
            indices: Index values from KDTree query
            
        Returns:
            list: Processed match results
        """
        database = self.db_manager.get_database()
        
        # Validate database structure
        required_keys = ['categories', 'image_ids', 'image_paths', 'contours']
        for key in required_keys:
            if key not in database or not isinstance(database[key], (list, np.ndarray)):
                print(f"CRITICAL ERROR: '{key}' is missing or not a list in database")
                return []
        
        num_items = len(database['contours'])
        results = []
        
        print(f"Processing {len(indices)} potential matches...")
        
        for dist, idx in zip(distances, indices):
            # Convert index to Python int for safe list indexing
            try:
                current_idx = int(idx)
            except (ValueError, TypeError) as e:
                print(f"ERROR: Cannot convert index '{idx}' to integer. Skipping. Error: {e}")
                continue
            
            print(f"Processing result with index: {current_idx}, distance: {dist}")
            
            # Boundary check
            if not (0 <= current_idx < num_items):
                print(f"Error: Retrieved index {current_idx} is out of bounds (0 <= index < {num_items})")
                continue
            
            try:
                # Extract metadata from database
                metadata = self._extract_metadata(database, current_idx)
                
                # Calculate similarity score
                similarity_score = calculate_similarity_score(dist)
                
                # Create result entry
                result = {
                    'category': metadata['category'],
                    'image_id': metadata['image_id'],
                    'image_path': metadata['image_path'],
                    'contour': metadata['contour'],
                    'similarity_score': similarity_score,
                    'distance': float(dist),
                    'index': current_idx
                }
                
                print(f"  Data retrieved - category: {metadata['category']}, image_id: {metadata['image_id']}")
                results.append(result)
                
            except IndexError:
                print(f"Error: Index {current_idx} out of bounds for one of the database arrays")
            except Exception as e:
                print(f"Error processing result at index {current_idx}: {e}")
        
        print(f"Pose matching finished, returning {len(results)} results")
        return results
    
    def _extract_metadata(self, database, index):
        """
        Extract metadata for a database entry by index
        
        Args:
            database: Database dictionary
            index: Index of the entry
            
        Returns:
            dict: Metadata for the entry
        """
        return {
            'category': database['categories'][index] if index < len(database['categories']) else "Unknown",
            'image_id': database['image_ids'][index] if index < len(database['image_ids']) else "",
            'image_path': database['image_paths'][index] if index < len(database['image_paths']) else "",
            'contour': database['contours'][index]
        }
    
    def match_with_filtering(self, human_contour, category_filter=None, min_similarity=0.0, top_k=DEFAULT_SIMILARITY_TOP_K):
        """
        Match poses with additional filtering options
        
        Args:
            human_contour: Human contour to match
            category_filter: Optional category to filter results
            min_similarity: Minimum similarity score threshold
            top_k: Number of top matches to return
            
        Returns:
            list: Filtered match results
        """
        # Get initial matches
        matches = self.match_pose(human_contour, top_k=top_k * 2)  # Get more to allow for filtering
        
        # Apply filters
        filtered_matches = []
        
        for match in matches:
            # Category filter
            if category_filter and match['category'].lower() != category_filter.lower():
                continue
            
            # Similarity threshold filter
            if match['similarity_score'] < min_similarity:
                continue
            
            filtered_matches.append(match)
            
            # Stop when we have enough results
            if len(filtered_matches) >= top_k:
                break
        
        return filtered_matches
    
    def get_match_statistics(self, human_contour, top_k=10):
        """
        Get detailed statistics about matches
        
        Args:
            human_contour: Human contour to analyze
            top_k: Number of matches to analyze
            
        Returns:
            dict: Match statistics
        """
        matches = self.match_pose(human_contour, top_k=top_k)
        
        if not matches:
            return {
                'total_matches': 0,
                'avg_similarity': 0,
                'best_match': None,
                'category_distribution': {}
            }
        
        # Calculate statistics
        similarities = [m['similarity_score'] for m in matches]
        categories = [m['category'] for m in matches]
        
        from collections import Counter
        category_counts = Counter(categories)
        
        return {
            'total_matches': len(matches),
            'avg_similarity': np.mean(similarities),
            'max_similarity': np.max(similarities),
            'min_similarity': np.min(similarities),
            'best_match': matches[0] if matches else None,
            'category_distribution': dict(category_counts),
            'most_common_category': category_counts.most_common(1)[0][0] if category_counts else None
        }
    
    def validate_matching_setup(self):
        """
        Validate that the matching setup is correct
        
        Returns:
            dict: Validation results
        """
        if not self.db_manager.is_loaded():
            return {
                'valid': False,
                'error': 'Database not loaded'
            }
        
        if not self.db_manager.is_indexed():
            return {
                'valid': False,
                'error': 'Database not indexed'
            }
        
        database = self.db_manager.get_database()
        features = self.db_manager.get_features()
        kdtree = self.db_manager.get_kdtree()
        
        # Check consistency
        num_contours = len(database.get('contours', []))
        num_features = features.shape[0] if features is not None else 0
        
        if num_contours != num_features:
            return {
                'valid': False,
                'error': f'Inconsistent data: {num_contours} contours vs {num_features} features'
            }
        
        return {
            'valid': True,
            'num_items': num_contours,
            'feature_dimensions': features.shape[1] if features is not None else 0,
            'kdtree_ready': kdtree is not None
        }


# Global pose matching engine instance
_pose_matcher = None


def get_pose_matcher():
    """
    Get global pose matching engine instance
    
    Returns:
        PoseMatchingEngine: Global matcher instance
    """
    global _pose_matcher
    if _pose_matcher is None:
        _pose_matcher = PoseMatchingEngine()
    return _pose_matcher


def match_pose(human_contour, top_k=DEFAULT_SIMILARITY_TOP_K):
    """
    Convenience function to match pose
    
    Args:
        human_contour: Human contour to match
        top_k: Number of matches to return
        
    Returns:
        list: Match results
    """
    matcher = get_pose_matcher()
    return matcher.match_pose(human_contour, top_k)


def match_with_filtering(human_contour, category_filter=None, min_similarity=0.0, top_k=DEFAULT_SIMILARITY_TOP_K):
    """
    Convenience function for filtered matching
    
    Args:
        human_contour: Human contour to match
        category_filter: Optional category filter
        min_similarity: Minimum similarity threshold
        top_k: Number of matches to return
        
    Returns:
        list: Filtered match results
    """
    matcher = get_pose_matcher()
    return matcher.match_with_filtering(human_contour, category_filter, min_similarity, top_k)


def get_match_statistics(human_contour, top_k=10):
    """
    Convenience function to get match statistics
    
    Args:
        human_contour: Human contour to analyze
        top_k: Number of matches to analyze
        
    Returns:
        dict: Match statistics
    """
    matcher = get_pose_matcher()
    return matcher.get_match_statistics(human_contour, top_k)


def initialize_pose_matcher(database_manager=None):
    """
    Initialize pose matcher with specific database manager
    
    Args:
        database_manager: Database manager instance
        
    Returns:
        PoseMatchingEngine: Initialized matcher
    """
    global _pose_matcher
    _pose_matcher = PoseMatchingEngine(database_manager)
    return _pose_matcher


def validate_matching_setup():
    """
    Convenience function to validate matching setup
    
    Returns:
        dict: Validation results
    """
    try:
        matcher = get_pose_matcher()
        return matcher.validate_matching_setup()
    except Exception as e:
        return {
            'valid': False,
            'error': str(e)
        }


def cleanup_pose_matcher():
    """
    Cleanup pose matcher resources
    """
    global _pose_matcher
    _pose_matcher = None 