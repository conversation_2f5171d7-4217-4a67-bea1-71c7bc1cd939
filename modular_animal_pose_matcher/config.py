#!/usr/bin/env python3
"""
Configuration module for Animal Pose Matcher

Contains all global configuration constants, file paths, API keys, and other settings.
"""

import os
from pathlib import Path

# Base directories
BASE_DIR = Path('/Users/<USER>/Desktop/pose_matching_project')
RESULTS_DIR = Path('/Users/<USER>/Desktop/results')
MUSEUM_DIR = Path('/Users/<USER>/Desktop/museum')
LABEL_MUSEUM_DIR = Path('/Users/<USER>/Desktop/label museum')
HOMEPAGE_PIC_DIR = Path('/Users/<USER>/Desktop/homepage pic')

# Model paths
HUMAN_MODEL_PATH = BASE_DIR / 'models' / 'human_seg_model.pt'
ANIMAL_DB_PATH = BASE_DIR / 'database' / 'animal_contours.pkl'

# API keys and external services
GEMINI_API_KEY = "AIzaSyAs1V__qlCjvp4nzoR7LmQ27b9D9wkL4Cg"

# Server configuration
DEFAULT_PORT = 8080
DEFAULT_WEB_MODE = 'web'
DEFAULT_CV_MODE = 'cv'

# Image processing constants
DEFAULT_CONTOUR_NORMALIZATION_SIZE = 100
MIN_CONTOUR_AREA = 10
CONTOUR_SIMPLIFICATION_EPSILON = 0.02
DEFAULT_SIMILARITY_TOP_K = 1

# Camera and capture settings
DEFAULT_CAMERA_INDEX = 0
COUNTDOWN_SECONDS = 3
CAPTURE_IMAGE_QUALITY = 0.7
REALTIME_CONTOUR_INTERVAL_MS = 200

# Visualization settings
CONTOUR_COLOR_GREEN = (0, 255, 0)
CONTOUR_COLOR_RED = (255, 0, 0)
CONTOUR_LINE_THICKNESS = 2
FIGURE_SIZE_SINGLE = (12, 6)
FIGURE_SIZE_MULTIPLE = (4, 5)

# File extensions and patterns
SUPPORTED_IMAGE_EXTENSIONS = ['.jpg', '.jpeg', '.png', '.JPG', '.JPEG', '.PNG']
TEMP_FILE_PREFIX = {
    'web_capture': 'web_capture_',
    'temp_frame': 'temp_frame_',
    'captured_image': 'captured_image_'
}

# HTTP response headers
CORS_HEADERS = {
    'Access-Control-Allow-Origin': '*',
    'Content-Type': 'application/json'
}

# Model configuration
YOLO_PERSON_CLASS_ID = 0  # COCO dataset person class
YOLO_GENERAL_MODEL = 'yolov8n.pt'

# Database and features configuration
SHAPE_FEATURES_COUNT = 11  # 7 Hu moments + circularity + aspect ratio + 2 orientation components
HU_MOMENTS_COUNT = 7
MIN_ELLIPSE_POINTS = 5
LOG_THRESHOLD = 1e-7

# Error messages
ERROR_MESSAGES = {
    'no_human_detected': 'No human contour detected',
    'no_contour_found': 'No human contour detected',
    'model_load_failed': 'Unable to load model',
    'database_load_failed': 'Unable to load animal contour database',
    'image_read_failed': 'Could not read image',
    'processing_failed': 'Error processing image',
    'no_results': 'No matching results found',
    'camera_access_failed': 'Could not access camera'
}

# Success messages
SUCCESS_MESSAGES = {
    'initialization_complete': 'Initialization complete!',
    'image_saved': 'Image saved to',
    'server_started': 'Animal Pose Matcher server started',
    'result_saved': 'Result saved!'
}

# File naming patterns
RESULT_FILES = {
    'human_pose': 'human_pose.jpg',
    'animal_pose': 'animal_pose.jpg',
    'result_html': 'result.html',
    'visualization': 'retrieval_results_{}.png'
}

# Museum location mapping
MUSEUM_LOCATIONS = {
    'Giraffe': 'African Mammals, Floor 1, South Wing',
    'Elephant': 'African Mammals, Floor 1, Central Hall',
    'Lion': 'African Predators, Floor 2, East Wing',
    'Zebra': 'African Plains, Floor 1, West Wing',
    'Rhino': 'Endangered Species, Floor 3, North Wing'
}

# Default animal information fallback data
DEFAULT_ANIMAL_INFO = {
    'conservation_status': 'Vulnerable',
    'description': 'This magnificent animal is part of our museum collection. Visit the exhibit to learn more about its habitat, behavior, and conservation status.',
    'diet': 'Varies by species',
    'habitat': 'Natural habitats across Africa',
    'lifespan': '15-30 years in the wild',
    'weight': 'Varies by species and age',
    'fun_fact': 'This animal is part of a critical conservation effort at our museum.'
}

# Specific animal information for fallback
ANIMAL_SPECIFIC_INFO = {
    'Giraffe': {
        'description': 'The giraffe is the tallest living terrestrial animal. Its distinctive features are its extremely long neck and legs, horn-like ossicones, and spotted coat patterns. Giraffes have specialized cardiovascular systems to manage blood pressure between their brain and heart.',
        'diet': 'Herbivore - primarily acacia leaves and shoots',
        'habitat': 'Savannas, grasslands, and open woodlands',
        'lifespan': '20-25 years in the wild',
        'weight': '800-1,930 kg (1,760-4,250 lb)',
        'fun_fact': 'Giraffes only need 5-30 minutes of sleep in a 24-hour period, often taken in very short naps.'
    },
    'Elephant': {
        'description': 'Elephants are the largest existing land animals and are known for their intelligence, long trunks, and tusks. They have complex social structures and are highly intelligent animals with excellent memory.',
        'diet': 'Herbivore - up to 300kg of vegetation daily',
        'habitat': 'Forests, deserts, marshes, and savannas',
        'lifespan': '60-70 years',
        'weight': '2,700-6,000 kg (6,000-13,000 lb)',
        'fun_fact': 'Elephants can recognize themselves in mirrors, showing self-awareness that few animals possess.'
    },
    'Lion': {
        'description': 'Lions are large cats belonging to the genus Panthera. They have muscular, deep-chested bodies and short, rounded heads. The male lion is distinguished by his mane, which is absent in female lions.',
        'diet': 'Carnivore - primarily large ungulates',
        'habitat': 'Grasslands, savannas, and open woodlands',
        'lifespan': '10-14 years in the wild',
        'weight': '150-250 kg (330-550 lb)',
        'fun_fact': 'Lions can roar so loudly it can be heard up to 8 kilometers away.'
    },
    'Zebra': {
        'description': 'Zebras are African equines with distinctive black-and-white striped coats. Each zebra has its own unique pattern of stripes, as distinctive as human fingerprints.',
        'diet': 'Herbivore - mainly grasses',
        'habitat': 'Plains, grasslands, and light woodlands',
        'lifespan': '25-30 years in the wild',
        'weight': '350-450 kg (770-990 lb)',
        'fun_fact': 'Each zebra has a unique stripe pattern, like human fingerprints.'
    },
    'Rhino': {
        'description': 'Rhinoceroses are large, herbivorous mammals identified by their characteristic horned snouts. Their thick protective skin is formed from layers of collagen.',
        'diet': 'Herbivore - grasses, leaves, fruits',
        'habitat': 'Grasslands and floodplains',
        'lifespan': '35-50 years',
        'weight': '1,800-2,700 kg (4,000-6,000 lb)',
        'fun_fact': 'Rhinos are known to have poor eyesight but excellent hearing and sense of smell.'
    }
}

def ensure_directories():
    """Ensure all required directories exist"""
    directories = [RESULTS_DIR, BASE_DIR / 'models', BASE_DIR / 'database']
    for directory in directories:
        directory.mkdir(parents=True, exist_ok=True)

def get_animal_info(animal_name):
    """Get animal information with fallback to default"""
    return ANIMAL_SPECIFIC_INFO.get(animal_name, DEFAULT_ANIMAL_INFO)

def get_museum_location(animal_name):
    """Get museum location for an animal"""
    return MUSEUM_LOCATIONS.get(animal_name, 'Main Exhibition Hall')

PROJECT_ROOT = Path(os.path.dirname(os.path.abspath(__file__)))

DATA_DIR = PROJECT_ROOT / "data"
COCO_DIR = DATA_DIR / "coco"
DATABASE_DIR = PROJECT_ROOT / "database"

MODELS_DIR = PROJECT_ROOT / "models"

COCO_URLS = {
    "val_images": "http://images.cocodataset.org/zips/val2017.zip",
    "annotations": "http://images.cocodataset.org/annotations/annotations_trainval2017.zip"
}

YOLO_CONF_THRESHOLD = 0.5
CONTOUR_MATCH_THRESHOLD = 0.8

for dir_path in [DATA_DIR, COCO_DIR, DATABASE_DIR, MODELS_DIR]:
    dir_path.mkdir(parents=True, exist_ok=True) 