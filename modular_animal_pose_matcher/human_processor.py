#!/usr/bin/env python3
"""
Human Processor module for Animal Pose Matcher

Handles human segmentation using YOLO models and contour extraction.
"""

import cv2
import numpy as np
from pathlib import Path
from config import YOLO_PERSON_CLASS_ID, ERROR_MESSAGES
from contour_utils import normalize_contour, extract_largest_contour, process_mask_to_contour
from model_manager import get_human_model


class HumanProcessor:
    """Processes human images for pose extraction"""
    
    def __init__(self, model=None):
        self.model = model or get_human_model()
        if self.model is None:
            raise Exception("Human segmentation model not loaded")
    
    def extract_human_contour(self, image_path):
        """
        Extract contour from human image using YOLO segmentation
        
        Args:
            image_path: Path to input image
            
        Returns:
            numpy.ndarray: Normalized human contour
            
        Raises:
            ValueError: If no human contour detected
        """
        # Use YOLO model for prediction
        results = self.model(str(image_path), verbose=False)
        
        # Get segmentation masks
        if not results or not results[0].masks:
            raise ValueError(ERROR_MESSAGES['no_human_detected'])
        
        # Get human masks (select the human detection result with highest confidence)
        person_masks = self._extract_person_masks(results[0])
        
        if not person_masks:
            raise ValueError(ERROR_MESSAGES['no_human_detected'])
        
        # Use the human mask with highest confidence
        mask = max(person_masks, key=lambda x: x[0])[1]
        
        # Process mask to extract contour
        contour = process_mask_to_contour(mask, normalize=True)
        
        if contour is None:
            raise ValueError(ERROR_MESSAGES['no_contour_found'])
        
        return contour
    
    def _extract_person_masks(self, result):
        """
        Extract person masks from YOLO result
        
        Args:
            result: YOLO result object
            
        Returns:
            list: List of (confidence, mask) tuples for person detections
        """
        person_masks = []
        
        if not hasattr(result, 'boxes') or not hasattr(result, 'masks'):
            return person_masks
        
        boxes = result.boxes.data
        masks = result.masks
        
        for i, box in enumerate(boxes):
            class_id = int(box[5])  # Class ID is at index 5
            confidence = float(box[4])  # Confidence is at index 4
            
            if class_id == YOLO_PERSON_CLASS_ID:  # Person class
                if i < len(masks):
                    mask = masks[i].data[0].cpu().numpy()
                    person_masks.append((confidence, mask))
        
        return person_masks
    
    def extract_contour_with_bbox(self, image_path):
        """
        Extract human contour along with bounding box information
        
        Args:
            image_path: Path to input image
            
        Returns:
            tuple: (contour, bbox_info) where bbox_info contains position data
        """
        contour = self.extract_human_contour(image_path)
        
        if contour is None:
            return None, None
        
        # Calculate bounding box
        x, y, w, h = cv2.boundingRect(contour)
        
        bbox_info = {
            'x': x,
            'y': y,
            'width': w,
            'height': h,
            'center_x': x + w // 2,
            'center_y': y + h // 2
        }
        
        return contour, bbox_info
    
    def process_realtime_frame(self, frame):
        """
        Process a realtime camera frame for contour extraction
        
        Args:
            frame: Camera frame (numpy array)
            
        Returns:
            dict: Processing result with contour data or None
        """
        try:
            # Use YOLO model for prediction on frame
            results = self.model(frame, verbose=False)
            
            if not results or not results[0].masks:
                return {
                    'success': False,
                    'error': 'No human detected in frame'
                }
            
            # Extract person masks
            person_masks = self._extract_person_masks(results[0])
            
            if not person_masks:
                return {
                    'success': False,
                    'error': 'No person masks found'
                }
            
            # Use mask with highest confidence
            mask = max(person_masks, key=lambda x: x[0])[1]
            
            # Extract contour from mask
            mask_uint8 = (mask * 255).astype(np.uint8)
            contours, _ = cv2.findContours(mask_uint8, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            
            if not contours:
                return {
                    'success': False,
                    'error': 'No contours found in mask'
                }
            
            # Get largest contour
            max_contour = max(contours, key=cv2.contourArea)
            
            # Get bounding box
            x, y, w, h = cv2.boundingRect(max_contour)
            
            return {
                'success': True,
                'contour': max_contour.tolist(),
                'bbox': [x, y, w, h],
                'image_width': frame.shape[1],
                'image_height': frame.shape[0]
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }
    
    def validate_image(self, image_path):
        """
        Validate that an image can be processed
        
        Args:
            image_path: Path to image file
            
        Returns:
            dict: Validation result
        """
        try:
            # Check if file exists
            if not Path(image_path).exists():
                return {
                    'valid': False,
                    'error': f'Image file not found: {image_path}'
                }
            
            # Try to read image
            img = cv2.imread(str(image_path))
            if img is None:
                return {
                    'valid': False,
                    'error': 'Unable to read image file'
                }
            
            # Check image dimensions
            h, w = img.shape[:2]
            if h < 100 or w < 100:
                return {
                    'valid': False,
                    'error': 'Image too small (minimum 100x100 pixels)'
                }
            
            return {
                'valid': True,
                'dimensions': (w, h),
                'channels': img.shape[2] if len(img.shape) > 2 else 1
            }
            
        except Exception as e:
            return {
                'valid': False,
                'error': str(e)
            }
    
    def get_segmentation_preview(self, image_path):
        """
        Get a preview of the segmentation result
        
        Args:
            image_path: Path to input image
            
        Returns:
            tuple: (original_image, segmented_image, contour)
        """
        # Read original image
        img = cv2.imread(str(image_path))
        if img is None:
            return None, None, None
        
        try:
            # Extract contour
            contour = self.extract_human_contour(image_path)
            
            # Create segmented image with contour overlay
            segmented_img = img.copy()
            
            # Resize contour to match image dimensions
            h, w = img.shape[:2]
            resize_factor = min(h, w) / 100.0
            resized_contour = (contour * resize_factor).astype(np.int32)
            
            # Center the contour
            x, y, cw, ch = cv2.boundingRect(resized_contour)
            offset_x = (w - cw) // 2 - x
            offset_y = (h - ch) // 2 - y
            centered_contour = resized_contour + [offset_x, offset_y]
            
            # Draw contour
            cv2.drawContours(segmented_img, [centered_contour], -1, (0, 255, 0), 2)
            
            return img, segmented_img, contour
            
        except Exception as e:
            print(f"Error generating preview: {e}")
            return img, None, None


# Global human processor instance
_human_processor = None


def get_human_processor():
    """
    Get global human processor instance
    
    Returns:
        HumanProcessor: Global processor instance
    """
    global _human_processor
    if _human_processor is None:
        _human_processor = HumanProcessor()
    return _human_processor


def extract_human_contour(image_path):
    """
    Convenience function to extract human contour
    
    Args:
        image_path: Path to input image
        
    Returns:
        numpy.ndarray: Extracted contour
    """
    processor = get_human_processor()
    return processor.extract_human_contour(image_path)


def process_realtime_frame(frame):
    """
    Convenience function for realtime frame processing
    
    Args:
        frame: Camera frame
        
    Returns:
        dict: Processing result
    """
    processor = get_human_processor()
    return processor.process_realtime_frame(frame)


def validate_image(image_path):
    """
    Convenience function to validate image
    
    Args:
        image_path: Path to image
        
    Returns:
        dict: Validation result
    """
    processor = get_human_processor()
    return processor.validate_image(image_path)


def initialize_human_processor(model=None):
    """
    Initialize human processor with specific model
    
    Args:
        model: YOLO model instance (optional)
        
    Returns:
        HumanProcessor: Initialized processor
    """
    global _human_processor
    _human_processor = HumanProcessor(model)
    return _human_processor


def cleanup_human_processor():
    """
    Cleanup human processor resources
    """
    global _human_processor
    _human_processor = None 