#!/usr/bin/env python3
"""
Contour utilities module for Animal Pose Matcher

Contains functions for computing shape features, contour normalization,
and geometric processing.
"""

import cv2
import numpy as np
from config import (
    HU_MOMENTS_COUNT, MIN_ELLIPSE_POINTS, LOG_THRESHOLD, 
    DEFAULT_CONTOUR_NORMALIZATION_SIZE
)


def compute_shape_features(contour):
    """
    Calculate shape features for contour
    
    Args:
        contour: OpenCV contour array
        
    Returns:
        numpy array: Feature vector containing Hu moments, circularity, 
                    aspect ratio, and orientation components
    """
    # Calculate Hu moments
    moments = cv2.moments(contour)
    if moments['m00'] != 0:
        hu_moments = cv2.HuMoments(moments).flatten()
    else:
        hu_moments = np.zeros(HU_MOMENTS_COUNT)

    # Safe logarithmic transformation
    log_hu = np.zeros_like(hu_moments)
    for i, hu in enumerate(hu_moments):
        if abs(hu) > LOG_THRESHOLD:  # Avoid taking log of zero
            log_hu[i] = -np.sign(hu) * np.log10(abs(hu))
        else:
            log_hu[i] = 0

    # Calculate area and perimeter ratio
    area = cv2.contourArea(contour)
    perimeter = cv2.arcLength(contour, True)
    if perimeter > 0:
        circularity = 4 * np.pi * area / (perimeter * perimeter)
    else:
        circularity = 0

    # Calculate contour orientation
    if len(contour) >= MIN_ELLIPSE_POINTS:  # Need at least 5 points to fit ellipse
        try:
            (x, y), (ma, mi), angle = cv2.fitEllipse(contour)
            aspect_ratio = ma / mi if mi > 0 else 1.0
        except:
            aspect_ratio = 1.0
            angle = 0
    else:
        aspect_ratio = 1.0
        angle = 0

    # Combine features
    features = np.concatenate([
        log_hu,  # Hu moments
        [circularity],  # Circularity
        [aspect_ratio],  # Aspect ratio
        [np.sin(angle * np.pi / 180), np.cos(angle * np.pi / 180)]  # Direction (sine and cosine)
    ])

    # Handle infinite values and NaN values
    features = np.nan_to_num(features, nan=0.0, posinf=0.0, neginf=0.0)

    return features


def normalize_contour(contour, target_size=DEFAULT_CONTOUR_NORMALIZATION_SIZE):
    """
    Normalize contour to a standard size and position
    
    Args:
        contour: OpenCV contour array
        target_size: Target size for normalization
        
    Returns:
        numpy array: Normalized contour
    """
    # Get bounding rectangle
    x, y, w, h = cv2.boundingRect(contour)
    
    # Translate contour to origin
    normalized_contour = contour - [x, y]
    
    # Scale contour to target size
    scale = target_size / max(w, h)
    normalized_contour = (normalized_contour * scale).astype(np.float32)
    
    return normalized_contour


def extract_largest_contour(mask):
    """
    Extract the largest contour from a binary mask
    
    Args:
        mask: Binary mask image
        
    Returns:
        numpy array: Largest contour, or None if no contours found
    """
    contours, _ = cv2.findContours(mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
    
    if not contours:
        return None
    
    # Get the largest contour
    max_contour = max(contours, key=cv2.contourArea)
    
    return max_contour


def process_mask_to_contour(mask, normalize=True):
    """
    Process a binary mask to extract and normalize the main contour
    
    Args:
        mask: Binary mask (numpy array)
        normalize: Whether to normalize the contour
        
    Returns:
        numpy array: Processed contour, or None if no valid contour found
    """
    # Ensure mask is in correct format
    if mask.dtype != np.uint8:
        mask = (mask * 255).astype(np.uint8)
    
    # Extract largest contour
    contour = extract_largest_contour(mask)
    
    if contour is None:
        return None
    
    # Normalize if requested
    if normalize:
        contour = normalize_contour(contour)
    
    return contour


def contour_area_filter(contour, min_area=100):
    """
    Check if contour meets minimum area requirement
    
    Args:
        contour: OpenCV contour
        min_area: Minimum area threshold
        
    Returns:
        bool: True if contour area is above threshold
    """
    if contour is None:
        return False
    
    area = cv2.contourArea(contour)
    return area >= min_area


def simplify_contour(contour, epsilon_factor=0.02):
    """
    Simplify contour using Douglas-Peucker algorithm
    
    Args:
        contour: OpenCV contour
        epsilon_factor: Approximation accuracy factor
        
    Returns:
        numpy array: Simplified contour
    """
    if contour is None:
        return None
    
    # Calculate epsilon as a percentage of contour perimeter
    perimeter = cv2.arcLength(contour, True)
    epsilon = epsilon_factor * perimeter
    
    # Approximate contour
    simplified = cv2.approxPolyDP(contour, epsilon, True)
    
    return simplified


def get_contour_center(contour):
    """
    Get the center point of a contour
    
    Args:
        contour: OpenCV contour
        
    Returns:
        tuple: (center_x, center_y) or None if invalid contour
    """
    if contour is None:
        return None
    
    moments = cv2.moments(contour)
    
    if moments['m00'] == 0:
        return None
    
    center_x = int(moments['m10'] / moments['m00'])
    center_y = int(moments['m01'] / moments['m00'])
    
    return (center_x, center_y)


def resize_contour_to_image(contour, image_shape, scale_factor=1.0):
    """
    Resize and position contour to fit in an image
    
    Args:
        contour: OpenCV contour
        image_shape: Target image shape (height, width)
        scale_factor: Scaling factor for the contour
        
    Returns:
        numpy array: Resized and positioned contour
    """
    if contour is None:
        return None
    
    img_h, img_w = image_shape[:2]
    
    # Calculate resize factor
    resize_factor = min(img_h, img_w) / DEFAULT_CONTOUR_NORMALIZATION_SIZE * scale_factor
    resized_contour = (contour * resize_factor).astype(np.int32)
    
    # Calculate center offset to position contour in image center
    x, y, w, h = cv2.boundingRect(resized_contour)
    center_offset_x = (img_w - w) // 2 - x
    center_offset_y = (img_h - h) // 2 - y
    
    centered_contour = resized_contour + [center_offset_x, center_offset_y]
    
    return centered_contour


def calculate_similarity_score(distance):
    """
    Convert distance to similarity score
    
    Args:
        distance: Distance value from KDTree query
        
    Returns:
        float: Similarity score between 0 and 1
    """
    return 1.0 / (1.0 + distance)


def contour_to_feature_vector(contour):
    """
    Convert contour to feature vector for matching
    
    Args:
        contour: OpenCV contour
        
    Returns:
        numpy array: Feature vector or None if contour is invalid
    """
    if contour is None:
        return None
    
    try:
        features = compute_shape_features(contour)
        return features
    except Exception as e:
        print(f"Error computing features for contour: {e}")
        return None 