#!/usr/bin/env python3
"""
Setup script for Animal Pose Matcher - Modular Version
"""

from setuptools import setup, find_packages
from pathlib import Path

# Read README file
README_PATH = Path(__file__).parent / "README.md"
long_description = README_PATH.read_text(encoding="utf-8") if README_PATH.exists() else ""

# Read requirements
REQUIREMENTS_PATH = Path(__file__).parent / "requirements.txt"
requirements = []
if REQUIREMENTS_PATH.exists():
    with open(REQUIREMENTS_PATH, 'r', encoding='utf-8') as f:
        requirements = [
            line.strip() 
            for line in f.readlines() 
            if line.strip() and not line.startswith('#')
        ]

setup(
    name="animal-pose-matcher",
    version="2.0.0",
    author="Animal Pose Matcher Team",
    author_email="<EMAIL>",
    description="A modular system for matching human poses with animal poses using deep learning",
    long_description=long_description,
    long_description_content_type="text/markdown",
    url="https://github.com/yourusername/animal-pose-matcher",
    packages=find_packages(),
    classifiers=[
        "Development Status :: 4 - Beta",
        "Intended Audience :: Developers",
        "Intended Audience :: Science/Research",
        "License :: OSI Approved :: MIT License",
        "Operating System :: OS Independent",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
        "Topic :: Scientific/Engineering :: Artificial Intelligence",
        "Topic :: Scientific/Engineering :: Image Recognition",
        "Topic :: Multimedia :: Graphics :: 3D Modeling",
    ],
    python_requires=">=3.8",
    install_requires=requirements,
    extras_require={
        "dev": [
            "pytest>=7.0.0",
            "black>=23.0.0",
            "flake8>=6.0.0",
            "mypy>=1.0.0",
        ],
        "web": [
            "flask>=2.3.0",
            "fastapi>=0.100.0",
            "uvicorn>=0.20.0",
        ],
    },
    entry_points={
        "console_scripts": [
            "animal-pose-matcher=main:main",
        ],
    },
    project_urls={
        "Bug Reports": "https://github.com/yourusername/animal-pose-matcher/issues",
        "Source": "https://github.com/yourusername/animal-pose-matcher",
        "Documentation": "https://github.com/yourusername/animal-pose-matcher/wiki",
    },
    keywords="computer-vision deep-learning pose-estimation animal-poses yolo opencv",
    include_package_data=True,
    zip_safe=False,
) 