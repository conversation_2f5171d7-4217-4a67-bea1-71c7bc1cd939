#!/usr/bin/env python3
"""
Model Manager module for Animal Pose Matcher

Handles loading and management of machine learning models including
human segmentation and animal recognition models.
"""

import os
from pathlib import Path
from ultralytics import YOLO
from config import (
    HUMAN_MODEL_PATH, YOLO_GENERAL_MODEL, 
    ERROR_MESSAGES, SUCCESS_MESSAGES
)


class ModelManager:
    """Manages loading and access to ML models"""
    
    def __init__(self):
        self.human_model = None
        self.animal_model = None
        self._models_loaded = False
    
    def load_human_segmentation_model(self, model_path=None):
        """
        Load human segmentation model
        
        Args:
            model_path: Path to human segmentation model file
            
        Returns:
            YOLO: Loaded YOLO model instance
            
        Raises:
            Exception: If model loading fails
        """
        if model_path is None:
            model_path = HUMAN_MODEL_PATH
        
        print("Loading human segmentation model...")
        
        try:
            self.human_model = YOLO(str(model_path))
            print(f"Human segmentation model loaded from: {model_path}")
            return self.human_model
        except Exception as e:
            error_msg = f"{ERROR_MESSAGES['model_load_failed']}: {e}"
            print(error_msg)
            raise Exception(error_msg)
    
    def load_animal_recognition_model(self, model_path=None):
        """
        Load animal recognition model
        
        Args:
            model_path: Path to animal recognition model file (optional)
            
        Returns:
            YOLO: Loaded YOLO model instance or None if failed
        """
        print("Loading animal recognition model...")
        
        try:
            if model_path and os.path.exists(model_path):
                self.animal_model = YOLO(str(model_path))
                print(f"Animal recognition model loaded from: {model_path}")
            else:
                # Use general YOLOv8 model as fallback
                self.animal_model = YOLO(YOLO_GENERAL_MODEL)
                print(f"Using general YOLO model: {YOLO_GENERAL_MODEL}")
            
            return self.animal_model
        except Exception as e:
            print(f"Unable to load animal recognition model: {e}")
            self.animal_model = None
            return None
    
    def load_all_models(self, human_model_path=None, animal_model_path=None):
        """
        Load all required models
        
        Args:
            human_model_path: Path to human segmentation model
            animal_model_path: Path to animal recognition model
            
        Returns:
            tuple: (human_model, animal_model)
        """
        human_model = self.load_human_segmentation_model(human_model_path)
        animal_model = self.load_animal_recognition_model(animal_model_path)
        
        self._models_loaded = True
        print(SUCCESS_MESSAGES['initialization_complete'])
        
        return human_model, animal_model
    
    def get_human_model(self):
        """
        Get human segmentation model instance
        
        Returns:
            YOLO: Human segmentation model or None if not loaded
        """
        return self.human_model
    
    def get_animal_model(self):
        """
        Get animal recognition model instance
        
        Returns:
            YOLO: Animal recognition model or None if not loaded
        """
        return self.animal_model
    
    def models_loaded(self):
        """
        Check if models are loaded
        
        Returns:
            bool: True if models are loaded
        """
        return self._models_loaded and self.human_model is not None
    
    def validate_model_files(self, human_model_path=None, animal_model_path=None):
        """
        Validate that model files exist
        
        Args:
            human_model_path: Path to human model file
            animal_model_path: Path to animal model file (optional)
            
        Returns:
            dict: Validation results
        """
        results = {
            'human_model_exists': False,
            'animal_model_exists': False,
            'human_model_path': human_model_path or HUMAN_MODEL_PATH,
            'animal_model_path': animal_model_path
        }
        
        # Check human model
        human_path = Path(human_model_path or HUMAN_MODEL_PATH)
        results['human_model_exists'] = human_path.exists()
        
        # Check animal model if provided
        if animal_model_path:
            animal_path = Path(animal_model_path)
            results['animal_model_exists'] = animal_path.exists()
        else:
            # General YOLO model should be downloadable
            results['animal_model_exists'] = True
        
        return results


# Global model manager instance
_model_manager = None


def get_model_manager():
    """
    Get global model manager instance (singleton pattern)
    
    Returns:
        ModelManager: Global model manager instance
    """
    global _model_manager
    if _model_manager is None:
        _model_manager = ModelManager()
    return _model_manager


def load_human_segmentation_model(model_path=None):
    """
    Convenience function to load human segmentation model
    
    Args:
        model_path: Path to model file
        
    Returns:
        YOLO: Loaded model instance
    """
    manager = get_model_manager()
    return manager.load_human_segmentation_model(model_path)


def load_animal_recognition_model(model_path=None):
    """
    Convenience function to load animal recognition model
    
    Args:
        model_path: Path to model file
        
    Returns:
        YOLO: Loaded model instance or None
    """
    manager = get_model_manager()
    return manager.load_animal_recognition_model(model_path)


def get_human_model():
    """
    Convenience function to get human model
    
    Returns:
        YOLO: Human model instance or None
    """
    manager = get_model_manager()
    return manager.get_human_model()


def get_animal_model():
    """
    Convenience function to get animal model
    
    Returns:
        YOLO: Animal model instance or None
    """
    manager = get_model_manager()
    return manager.get_animal_model()


def initialize_models(human_model_path=None, animal_model_path=None):
    """
    Initialize all models with validation
    
    Args:
        human_model_path: Path to human segmentation model
        animal_model_path: Path to animal recognition model
        
    Returns:
        tuple: (human_model, animal_model)
        
    Raises:
        Exception: If critical models fail to load
    """
    manager = get_model_manager()
    
    # Validate model files first
    validation = manager.validate_model_files(human_model_path, animal_model_path)
    
    if not validation['human_model_exists']:
        raise Exception(f"Human segmentation model not found: {validation['human_model_path']}")
    
    # Load models
    return manager.load_all_models(human_model_path, animal_model_path)


def cleanup_models():
    """
    Cleanup model resources
    """
    global _model_manager
    if _model_manager:
        _model_manager.human_model = None
        _model_manager.animal_model = None
        _model_manager._models_loaded = False
        _model_manager = None 