#!/usr/bin/env python3
"""
External APIs Manager module for Animal Pose Matcher

Handles interactions with external APIs like Google Gemini for animal information.
"""

import requests
import traceback
import google.generativeai as genai
from config import GEMINI_API_KEY, get_animal_info as get_fallback_animal_info


class ExternalAPIsManager:
    """Manages external API interactions"""
    
    def __init__(self, api_key=None):
        self.gemini_api_key = api_key or GEMINI_API_KEY
        self._configure_gemini()
    
    def _configure_gemini(self):
        """Configure Google Gemini API"""
        try:
            if self.gemini_api_key:
                genai.configure(api_key=self.gemini_api_key)
                self.gemini_configured = True
                print("Google Gemini API configured successfully")
            else:
                self.gemini_configured = False
                print("Warning: No Gemini API key provided")
        except Exception as e:
            print(f"Error configuring Gemini API: {e}")
            self.gemini_configured = False
    
    def get_animal_info(self, animal_name):
        """
        Get comprehensive animal information from Gemini API
        
        Args:
            animal_name: Name of the animal
            
        Returns:
            dict: Animal information or fallback data
        """
        if not self.gemini_configured:
            print("Gemini API not configured, using fallback data")
            return get_fallback_animal_info(animal_name)
        
        try:
            # Create prompt for Gemini
            prompt = f"""
            请提供关于动物"{animal_name}"的详细信息，包括以下方面：
            1. 保护状态（如濒危、脆弱、无危等）
            2. 详细描述（外观特征、行为习性等）
            3. 饮食习惯
            4. 自然栖息地
            5. 平均寿命
            6. 体重范围
            7. 一个有趣的事实
            
            请以JSON格式返回，包含以下键：
            - conservation_status
            - description
            - diet
            - habitat
            - lifespan
            - weight
            - fun_fact
            
            请用中文回答，但JSON键名用英文。
            """
            
            print(f"Requesting information for animal: {animal_name}")
            
            # Use Gemini API
            model = genai.GenerativeModel('gemini-pro')
            response = model.generate_content(prompt)
            
            if response and response.text:
                # Try to parse JSON response
                import json
                try:
                    # Extract JSON from response text
                    response_text = response.text.strip()
                    
                    # Find JSON content between ```json and ``` or just parse directly
                    if '```json' in response_text:
                        json_start = response_text.find('```json') + 7
                        json_end = response_text.find('```', json_start)
                        json_content = response_text[json_start:json_end].strip()
                    elif '{' in response_text and '}' in response_text:
                        # Try to extract JSON object
                        start = response_text.find('{')
                        end = response_text.rfind('}') + 1
                        json_content = response_text[start:end]
                    else:
                        json_content = response_text
                    
                    animal_info = json.loads(json_content)
                    
                    # Validate required keys and add missing ones
                    required_keys = ['conservation_status', 'description', 'diet', 'habitat', 'lifespan', 'weight', 'fun_fact']
                    fallback_info = get_fallback_animal_info(animal_name)
                    
                    for key in required_keys:
                        if key not in animal_info or not animal_info[key]:
                            animal_info[key] = fallback_info.get(key, "信息暂不可用")
                    
                    print(f"Successfully retrieved information for {animal_name}")
                    return animal_info
                    
                except json.JSONDecodeError as e:
                    print(f"JSON parsing error: {e}")
                    print(f"Raw response: {response.text}")
                    # Return fallback if JSON parsing fails
                    return self._create_fallback_response(animal_name, response.text)
            else:
                print("Empty response from Gemini API")
                return get_fallback_animal_info(animal_name)
                
        except Exception as e:
            print(f"Error getting animal info from Gemini: {e}")
            print(f"Traceback: {traceback.format_exc()}")
            return get_fallback_animal_info(animal_name)
    
    def _create_fallback_response(self, animal_name, raw_response):
        """
        Create a fallback response when JSON parsing fails
        
        Args:
            animal_name: Name of the animal
            raw_response: Raw response text from API
            
        Returns:
            dict: Fallback animal information
        """
        fallback_info = get_fallback_animal_info(animal_name)
        
        # Try to extract useful information from raw response
        if raw_response:
            # Simple text parsing for key information
            response_lower = raw_response.lower()
            
            # Extract conservation status if mentioned
            if '濒危' in raw_response or 'endangered' in response_lower:
                fallback_info['conservation_status'] = '濒危'
            elif '脆弱' in raw_response or 'vulnerable' in response_lower:
                fallback_info['conservation_status'] = '脆弱'
            elif '无危' in raw_response or 'least concern' in response_lower:
                fallback_info['conservation_status'] = '无危'
            
            # Use first part of response as description if it's long enough
            if len(raw_response) > 100:
                # Take first 200 characters as description
                fallback_info['description'] = raw_response[:200] + "..."
        
        return fallback_info
    
    def test_api_connection(self):
        """
        Test connection to external APIs
        
        Returns:
            dict: Test results
        """
        results = {
            'gemini_configured': self.gemini_configured,
            'gemini_working': False
        }
        
        if self.gemini_configured:
            try:
                # Test with a simple request
                model = genai.GenerativeModel('gemini-pro')
                response = model.generate_content("Hello, this is a test.")
                
                if response and response.text:
                    results['gemini_working'] = True
                    results['test_response'] = response.text[:100]
                else:
                    results['error'] = 'Empty response from Gemini'
                    
            except Exception as e:
                results['error'] = str(e)
        
        return results
    
    def get_multiple_animal_info(self, animal_names):
        """
        Get information for multiple animals
        
        Args:
            animal_names: List of animal names
            
        Returns:
            dict: Dictionary mapping animal names to their information
        """
        results = {}
        
        for animal_name in animal_names:
            print(f"Getting info for {animal_name}...")
            results[animal_name] = self.get_animal_info(animal_name)
        
        return results
    
    def get_animal_conservation_status(self, animal_name):
        """
        Get only conservation status for an animal
        
        Args:
            animal_name: Name of the animal
            
        Returns:
            str: Conservation status
        """
        info = self.get_animal_info(animal_name)
        return info.get('conservation_status', 'Unknown')
    
    def update_api_key(self, new_api_key):
        """
        Update API key and reconfigure
        
        Args:
            new_api_key: New Gemini API key
        """
        self.gemini_api_key = new_api_key
        self._configure_gemini()


# Global external APIs manager instance
_apis_manager = None


def get_apis_manager():
    """
    Get global external APIs manager instance
    
    Returns:
        ExternalAPIsManager: Global manager instance
    """
    global _apis_manager
    if _apis_manager is None:
        _apis_manager = ExternalAPIsManager()
    return _apis_manager


def get_animal_info(animal_name):
    """
    Convenience function to get animal information
    
    Args:
        animal_name: Name of the animal
        
    Returns:
        dict: Animal information
    """
    manager = get_apis_manager()
    return manager.get_animal_info(animal_name)


def test_api_connection():
    """
    Convenience function to test API connection
    
    Returns:
        dict: Test results
    """
    manager = get_apis_manager()
    return manager.test_api_connection()


def get_animal_conservation_status(animal_name):
    """
    Convenience function to get conservation status
    
    Args:
        animal_name: Name of the animal
        
    Returns:
        str: Conservation status
    """
    manager = get_apis_manager()
    return manager.get_animal_conservation_status(animal_name)


def initialize_apis_manager(api_key=None):
    """
    Initialize APIs manager with specific API key
    
    Args:
        api_key: Gemini API key
        
    Returns:
        ExternalAPIsManager: Initialized manager
    """
    global _apis_manager
    _apis_manager = ExternalAPIsManager(api_key)
    return _apis_manager


def cleanup_apis_manager():
    """
    Cleanup APIs manager resources
    """
    global _apis_manager
    _apis_manager = None 