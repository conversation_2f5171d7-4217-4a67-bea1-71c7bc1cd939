#!/usr/bin/env python3
"""
Database Manager module for Animal Pose Matcher

Handles loading and indexing of the animal contour database.
"""

import pickle
import numpy as np
from pathlib import Path
from scipy.spatial import KDTree
from config import ANIMAL_DB_PATH, ERROR_MESSAGES
from contour_utils import compute_shape_features


class DatabaseManager:
    """Manages animal contour database and indexing"""
    
    def __init__(self, db_path=None):
        self.db_path = Path(db_path) if db_path else ANIMAL_DB_PATH
        self.animal_db = None
        self.features = None
        self.kdtree = None
        self._db_loaded = False
    
    def load_database(self, db_path=None):
        """
        Load animal contour database from pickle file
        
        Args:
            db_path: Path to database pickle file
            
        Returns:
            dict: Loaded database content
            
        Raises:
            Exception: If database loading fails
        """
        if db_path:
            self.db_path = Path(db_path)
        
        print("Loading animal contour database...")
        
        try:
            with open(self.db_path, 'rb') as f:
                self.animal_db = pickle.load(f)
            
            print(f"Database loaded from: {self.db_path}")
            print(f"Database contains {len(self.animal_db.get('contours', []))} contours")
            
            self._db_loaded = True
            return self.animal_db
            
        except FileNotFoundError:
            error_msg = f"Database file not found: {self.db_path}"
            print(error_msg)
            raise Exception(error_msg)
        except Exception as e:
            error_msg = f"{ERROR_MESSAGES['database_load_failed']}: {e}"
            print(error_msg)
            raise Exception(error_msg)
    
    def build_index(self):
        """
        Build KD-tree index for fast similarity search
        
        Returns:
            KDTree: Built KDTree index
            
        Raises:
            Exception: If index building fails
        """
        if not self._db_loaded or not self.animal_db:
            raise Exception("Database must be loaded before building index")
        
        print("Building KD-tree index...")
        
        try:
            # Calculate features for each contour
            self.features = []
            contours = self.animal_db.get('contours', [])
            
            for i, contour in enumerate(contours):
                if i % 100 == 0:  # Progress indicator
                    print(f"Processing contour {i}/{len(contours)}")
                
                features = compute_shape_features(contour)
                self.features.append(features)
            
            # Convert feature list to numpy array
            self.features = np.array(self.features)
            
            # Handle infinite values and NaN values
            self.features = np.nan_to_num(self.features, nan=0.0, posinf=0.0, neginf=0.0)
            
            print(f"Feature matrix shape: {self.features.shape}")
            
            # Build KD-tree
            self.kdtree = KDTree(self.features)
            
            print("KD-tree index built successfully")
            return self.kdtree
            
        except Exception as e:
            error_msg = f"Failed to build index: {e}"
            print(error_msg)
            raise Exception(error_msg)
    
    def get_database(self):
        """
        Get loaded database
        
        Returns:
            dict: Animal database or None if not loaded
        """
        return self.animal_db
    
    def get_features(self):
        """
        Get computed features array
        
        Returns:
            numpy.ndarray: Features array or None if not computed
        """
        return self.features
    
    def get_kdtree(self):
        """
        Get KDTree index
        
        Returns:
            KDTree: KDTree index or None if not built
        """
        return self.kdtree
    
    def is_loaded(self):
        """
        Check if database is loaded
        
        Returns:
            bool: True if database is loaded
        """
        return self._db_loaded and self.animal_db is not None
    
    def is_indexed(self):
        """
        Check if database is indexed
        
        Returns:
            bool: True if KDTree index is built
        """
        return self.kdtree is not None
    
    def get_database_info(self):
        """
        Get information about the loaded database
        
        Returns:
            dict: Database information
        """
        if not self.is_loaded():
            return {'loaded': False}
        
        info = {
            'loaded': True,
            'indexed': self.is_indexed(),
            'path': str(self.db_path),
            'num_contours': len(self.animal_db.get('contours', [])),
            'categories': len(set(self.animal_db.get('categories', []))),
            'feature_dimensions': self.features.shape[1] if self.features is not None else None
        }
        
        # Add category distribution
        if 'categories' in self.animal_db:
            from collections import Counter
            category_counts = Counter(self.animal_db['categories'])
            info['category_distribution'] = dict(category_counts)
        
        return info
    
    def validate_database_structure(self):
        """
        Validate that the database has the expected structure
        
        Returns:
            dict: Validation results
        """
        if not self.is_loaded():
            return {
                'valid': False,
                'error': 'Database not loaded'
            }
        
        required_keys = ['contours', 'categories', 'image_ids', 'image_paths']
        missing_keys = []
        
        for key in required_keys:
            if key not in self.animal_db:
                missing_keys.append(key)
        
        if missing_keys:
            return {
                'valid': False,
                'error': f'Missing required keys: {missing_keys}'
            }
        
        # Check if all arrays have the same length
        lengths = {key: len(self.animal_db[key]) for key in required_keys}
        if len(set(lengths.values())) > 1:
            return {
                'valid': False,
                'error': f'Inconsistent array lengths: {lengths}'
            }
        
        return {
            'valid': True,
            'structure': lengths
        }
    
    def get_contour_by_index(self, index):
        """
        Get contour by index
        
        Args:
            index: Index of the contour
            
        Returns:
            numpy.ndarray: Contour or None if invalid index
        """
        if not self.is_loaded():
            return None
        
        contours = self.animal_db.get('contours', [])
        if 0 <= index < len(contours):
            return contours[index]
        
        return None
    
    def get_metadata_by_index(self, index):
        """
        Get metadata for a contour by index
        
        Args:
            index: Index of the contour
            
        Returns:
            dict: Metadata or None if invalid index
        """
        if not self.is_loaded():
            return None
        
        contours = self.animal_db.get('contours', [])
        if not (0 <= index < len(contours)):
            return None
        
        metadata = {
            'index': index,
            'category': self.animal_db.get('categories', [None])[index],
            'image_id': self.animal_db.get('image_ids', [None])[index],
            'image_path': self.animal_db.get('image_paths', [None])[index]
        }
        
        return metadata


# Global database manager instance
_database_manager = None


def get_database_manager():
    """
    Get global database manager instance (singleton pattern)
    
    Returns:
        DatabaseManager: Global database manager instance
    """
    global _database_manager
    if _database_manager is None:
        _database_manager = DatabaseManager()
    return _database_manager


def load_database(db_path=None):
    """
    Convenience function to load database
    
    Args:
        db_path: Path to database file
        
    Returns:
        dict: Loaded database
    """
    manager = get_database_manager()
    return manager.load_database(db_path)


def build_index():
    """
    Convenience function to build KDTree index
    
    Returns:
        KDTree: Built index
    """
    manager = get_database_manager()
    return manager.build_index()


def get_database():
    """
    Convenience function to get database
    
    Returns:
        dict: Database or None
    """
    manager = get_database_manager()
    return manager.get_database()


def get_kdtree():
    """
    Convenience function to get KDTree
    
    Returns:
        KDTree: KDTree index or None
    """
    manager = get_database_manager()
    return manager.get_kdtree()


def initialize_database(db_path=None):
    """
    Initialize database with loading and indexing
    
    Args:
        db_path: Path to database file
        
    Returns:
        tuple: (database, kdtree)
    """
    manager = get_database_manager()
    
    # Load database
    database = manager.load_database(db_path)
    
    # Validate structure
    validation = manager.validate_database_structure()
    if not validation['valid']:
        raise Exception(f"Invalid database structure: {validation['error']}")
    
    # Build index
    kdtree = manager.build_index()
    
    print("Database initialization complete")
    return database, kdtree


def cleanup_database():
    """
    Cleanup database resources
    """
    global _database_manager
    if _database_manager:
        _database_manager.animal_db = None
        _database_manager.features = None
        _database_manager.kdtree = None
        _database_manager._db_loaded = False
        _database_manager = None 