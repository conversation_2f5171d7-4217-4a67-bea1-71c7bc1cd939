#!/usr/bin/env python3
"""
Animal Processor module for Animal Pose Matcher

Handles processing animal images and extracting information from JSON annotations.
"""

import os
import cv2
import json
import numpy as np
from pathlib import Path
from config import LABEL_MUSEUM_DIR, MUSEUM_DIR, SUPPORTED_IMAGE_EXTENSIONS
from model_manager import get_animal_model


class AnimalProcessor:
    """Processes animal images and annotations"""
    
    def __init__(self, model=None):
        self.model = model or get_animal_model()
        self.label_dir = LABEL_MUSEUM_DIR
        self.image_dir = MUSEUM_DIR
    
    def detect_animal_in_image(self, image_path):
        """
        Detect animal type and mask from JSON annotation file
        
        Args:
            image_path: Path to the animal image
            
        Returns:
            tuple: (animal_type, animal_mask) or (None, None) if not found
        """
        try:
            # Get image filename without extension
            image_filename = os.path.basename(image_path)
            image_name = os.path.splitext(image_filename)[0]
            
            # Find corresponding JSON annotation file
            json_file_path = self.label_dir / f"{image_name}.json"
            
            if not json_file_path.exists():
                print(f"No annotation file found: {json_file_path}")
                return None, None
            
            # Read original image to get dimensions
            orig_img = cv2.imread(str(image_path))
            if orig_img is None:
                return None, None
            
            orig_h, orig_w = orig_img.shape[:2]
            
            # Read JSON annotation file
            with open(json_file_path, 'r') as f:
                annotation_data = json.load(f)
            
            # Extract shapes from annotation
            shapes = annotation_data.get('shapes', [])
            if not shapes:
                return None, None
            
            # Use first shape as primary animal
            primary_shape = shapes[0]
            animal_type = primary_shape.get('label', 'Unknown')
            
            # Create mask from annotation
            mask = self._create_mask_from_annotation(annotation_data, orig_w, orig_h)
            
            return animal_type, mask
            
        except Exception as e:
            print(f"Error processing animal annotation: {e}")
            return None, None
    
    def _create_mask_from_annotation(self, annotation_data, img_width, img_height):
        """
        Create binary mask from JSON annotation data
        
        Args:
            annotation_data: Parsed JSON annotation
            img_width: Image width
            img_height: Image height
            
        Returns:
            numpy.ndarray: Binary mask or None if invalid
        """
        try:
            shapes = annotation_data.get('shapes', [])
            if not shapes:
                return None
            
            # Create empty mask
            mask = np.zeros((img_height, img_width), dtype=np.uint8)
            
            # Process each shape in annotation
            for shape in shapes:
                shape_type = shape.get('shape_type', '')
                points = shape.get('points', [])
                
                if not points:
                    continue
                
                points_array = np.array(points, dtype=np.int32)
                
                if shape_type == 'polygon':
                    # Fill polygon
                    cv2.fillPoly(mask, [points_array], 255)
                elif shape_type == 'rectangle':
                    # Fill rectangle
                    if len(points_array) >= 2:
                        pt1 = tuple(points_array[0])
                        pt2 = tuple(points_array[1])
                        cv2.rectangle(mask, pt1, pt2, 255, -1)
            
            return mask
            
        except Exception as e:
            print(f"Error creating mask from annotation: {e}")
            return None
    
    def get_valid_animal_images(self):
        """
        Get list of valid animal images that have corresponding annotations
        
        Returns:
            list: List of valid image paths with annotations
        """
        valid_images = []
        
        if not self.image_dir.exists() or not self.label_dir.exists():
            return valid_images
        
        # Get all JSON annotation files
        json_files = list(self.label_dir.glob('*.json'))
        json_map = {f.stem: f for f in json_files}
        
        # Find corresponding images
        for ext in SUPPORTED_IMAGE_EXTENSIONS:
            for img_file in self.image_dir.glob(f'*{ext}'):
                img_name = img_file.stem
                if img_name in json_map:
                    valid_images.append(img_file)
        
        return sorted(valid_images)
    
    def get_animal_by_category(self, category):
        """
        Get animal images filtered by category
        
        Args:
            category: Animal category to filter by
            
        Returns:
            list: List of image paths matching the category
        """
        matching_images = []
        valid_images = self.get_valid_animal_images()
        
        for img_path in valid_images:
            animal_type, _ = self.detect_animal_in_image(img_path)
            if animal_type and animal_type.lower() == category.lower():
                matching_images.append(img_path)
        
        return matching_images
    
    def validate_annotation_structure(self, json_path):
        """
        Validate annotation file structure
        
        Args:
            json_path: Path to JSON annotation file
            
        Returns:
            dict: Validation result
        """
        try:
            with open(json_path, 'r') as f:
                data = json.load(f)
            
            required_fields = ['shapes', 'imageWidth', 'imageHeight']
            missing_fields = []
            
            for field in required_fields:
                if field not in data:
                    missing_fields.append(field)
            
            if missing_fields:
                return {
                    'valid': False,
                    'error': f'Missing required fields: {missing_fields}'
                }
            
            # Validate shapes
            shapes = data.get('shapes', [])
            if not shapes:
                return {
                    'valid': False,
                    'error': 'No shapes found in annotation'
                }
            
            # Check each shape
            for i, shape in enumerate(shapes):
                if 'label' not in shape:
                    return {
                        'valid': False,
                        'error': f'Shape {i} missing label'
                    }
                
                if 'points' not in shape:
                    return {
                        'valid': False,
                        'error': f'Shape {i} missing points'
                    }
            
            return {
                'valid': True,
                'num_shapes': len(shapes),
                'image_dimensions': (data['imageWidth'], data['imageHeight'])
            }
            
        except Exception as e:
            return {
                'valid': False,
                'error': str(e)
            }
    
    def get_annotation_statistics(self):
        """
        Get statistics about available annotations
        
        Returns:
            dict: Statistics about annotations and categories
        """
        json_files = list(self.label_dir.glob('*.json')) if self.label_dir.exists() else []
        valid_images = self.get_valid_animal_images()
        
        categories = {}
        total_shapes = 0
        
        for json_file in json_files:
            try:
                with open(json_file, 'r') as f:
                    data = json.load(f)
                
                shapes = data.get('shapes', [])
                total_shapes += len(shapes)
                
                for shape in shapes:
                    label = shape.get('label', 'Unknown')
                    categories[label] = categories.get(label, 0) + 1
                    
            except Exception as e:
                print(f"Error reading annotation {json_file}: {e}")
                continue
        
        return {
            'total_annotations': len(json_files),
            'valid_image_pairs': len(valid_images),
            'total_shapes': total_shapes,
            'categories': categories,
            'most_common_category': max(categories.items(), key=lambda x: x[1])[0] if categories else None
        }
    
    def extract_animal_features_from_mask(self, mask):
        """
        Extract features from animal mask
        
        Args:
            mask: Binary mask of animal
            
        Returns:
            dict: Extracted features
        """
        if mask is None:
            return None
        
        try:
            # Find contours
            contours, _ = cv2.findContours(mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            
            if not contours:
                return None
            
            # Get largest contour
            main_contour = max(contours, key=cv2.contourArea)
            
            # Calculate basic features
            area = cv2.contourArea(main_contour)
            perimeter = cv2.arcLength(main_contour, True)
            
            # Bounding rectangle
            x, y, w, h = cv2.boundingRect(main_contour)
            
            # Aspect ratio
            aspect_ratio = w / h if h > 0 else 0
            
            # Circularity
            circularity = 4 * np.pi * area / (perimeter * perimeter) if perimeter > 0 else 0
            
            return {
                'area': area,
                'perimeter': perimeter,
                'bounding_box': (x, y, w, h),
                'aspect_ratio': aspect_ratio,
                'circularity': circularity,
                'contour': main_contour
            }
            
        except Exception as e:
            print(f"Error extracting features from mask: {e}")
            return None


# Global animal processor instance
_animal_processor = None


def get_animal_processor():
    """
    Get global animal processor instance
    
    Returns:
        AnimalProcessor: Global processor instance
    """
    global _animal_processor
    if _animal_processor is None:
        _animal_processor = AnimalProcessor()
    return _animal_processor


def detect_animal_in_image(image_path):
    """
    Convenience function to detect animal in image
    
    Args:
        image_path: Path to image
        
    Returns:
        tuple: (animal_type, mask)
    """
    processor = get_animal_processor()
    return processor.detect_animal_in_image(image_path)


def get_valid_animal_images():
    """
    Convenience function to get valid animal images
    
    Returns:
        list: Valid image paths
    """
    processor = get_animal_processor()
    return processor.get_valid_animal_images()


def get_animal_by_category(category):
    """
    Convenience function to get animals by category
    
    Args:
        category: Animal category
        
    Returns:
        list: Matching image paths
    """
    processor = get_animal_processor()
    return processor.get_animal_by_category(category)


def initialize_animal_processor(model=None):
    """
    Initialize animal processor with specific model
    
    Args:
        model: Animal recognition model (optional)
        
    Returns:
        AnimalProcessor: Initialized processor
    """
    global _animal_processor
    _animal_processor = AnimalProcessor(model)
    return _animal_processor


def cleanup_animal_processor():
    """
    Cleanup animal processor resources
    """
    global _animal_processor
    _animal_processor = None 