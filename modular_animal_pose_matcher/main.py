#!/usr/bin/env python3
"""
Animal Pose Matcher - Main Application Entry Point

This script serves as the main entry point for the Animal Pose Matcher application.
It handles initialization of all modules and provides both web and OpenCV modes.

Usage:
    python3 main.py [--mode web|cv] [--port 8080] [--models-dir path] [--db-path path]

Controls (CV mode):
    - Press SPACE to take a photo
    - Press 'q' to quit
"""

import argparse
import sys
import traceback
from pathlib import Path

# Import all our modules
import config
from model_manager import initialize_models
from database_manager import initialize_database
from human_processor import initialize_human_processor
from animal_processor import initialize_animal_processor
from pose_matching_engine import initialize_pose_matcher
from external_apis_manager import initialize_apis_manager


def parse_arguments():
    """Parse command line arguments"""
    parser = argparse.ArgumentParser(description='Animal Pose Matcher Application')
    
    parser.add_argument(
        '--mode', 
        choices=['web', 'cv'], 
        default=config.DEFAULT_WEB_MODE,
        help='Application mode: web server or OpenCV camera (default: web)'
    )
    
    parser.add_argument(
        '--port', 
        type=int, 
        default=config.DEFAULT_PORT,
        help=f'Web server port (default: {config.DEFAULT_PORT})'
    )
    
    parser.add_argument(
        '--human-model', 
        type=str,
        default=str(config.HUMAN_MODEL_PATH),
        help='Path to human segmentation model'
    )
    
    parser.add_argument(
        '--animal-model', 
        type=str,
        help='Path to animal recognition model (optional)'
    )
    
    parser.add_argument(
        '--db-path', 
        type=str,
        default=str(config.ANIMAL_DB_PATH),
        help='Path to animal contour database'
    )
    
    parser.add_argument(
        '--api-key', 
        type=str,
        help='Google Gemini API key (optional)'
    )
    
    parser.add_argument(
        '--results-dir', 
        type=str,
        default=str(config.RESULTS_DIR),
        help='Directory for saving results'
    )
    
    parser.add_argument(
        '--verbose', 
        action='store_true',
        help='Enable verbose logging'
    )
    
    parser.add_argument(
        '--test', 
        action='store_true',
        help='Run system tests and exit'
    )
    
    return parser.parse_args()


def validate_paths(args):
    """Validate that required paths exist"""
    errors = []
    
    # Check human model
    if not Path(args.human_model).exists():
        errors.append(f"Human segmentation model not found: {args.human_model}")
    
    # Check database
    if not Path(args.db_path).exists():
        errors.append(f"Animal contour database not found: {args.db_path}")
    
    # Check animal model if provided
    if args.animal_model and not Path(args.animal_model).exists():
        errors.append(f"Animal recognition model not found: {args.animal_model}")
    
    # Check results directory (create if doesn't exist)
    results_path = Path(args.results_dir)
    try:
        results_path.mkdir(parents=True, exist_ok=True)
    except Exception as e:
        errors.append(f"Cannot create results directory {args.results_dir}: {e}")
    
    return errors


def initialize_system(args):
    """Initialize all system components"""
    print("🚀 正在初始化动物姿态匹配系统...")
    
    try:
        # Ensure required directories exist
        config.ensure_directories()
        
        # Initialize models
        print("📚 正在加载机器学习模型...")
        human_model, animal_model = initialize_models(
            human_model_path=args.human_model,
            animal_model_path=args.animal_model
        )
        
        # Initialize database
        print("🗄️ 正在加载动物轮廓数据库...")
        database, kdtree = initialize_database(args.db_path)
        
        # Initialize processors
        print("🔧 正在初始化处理器...")
        human_processor = initialize_human_processor(human_model)
        animal_processor = initialize_animal_processor(animal_model)
        
        # Initialize matching engine
        print("🎯 正在初始化姿态匹配引擎...")
        pose_matcher = initialize_pose_matcher()
        
        # Initialize external APIs
        print("🌐 正在初始化外部API...")
        apis_manager = initialize_apis_manager(args.api_key)
        
        print("✅ 系统初始化完成！")
        
        return {
            'human_model': human_model,
            'animal_model': animal_model,
            'database': database,
            'kdtree': kdtree,
            'human_processor': human_processor,
            'animal_processor': animal_processor,
            'pose_matcher': pose_matcher,
            'apis_manager': apis_manager
        }
        
    except Exception as e:
        print(f"❌ 系统初始化失败: {e}")
        if args.verbose:
            print(traceback.format_exc())
        raise


def run_system_tests():
    """Run comprehensive system tests"""
    print("🧪 正在运行系统测试...")
    
    tests_passed = 0
    tests_total = 0
    
    try:
        # Test model manager
        print("测试模型管理器...")
        from model_manager import get_model_manager
        model_manager = get_model_manager()
        if model_manager.models_loaded():
            print("✅ 模型管理器正常")
            tests_passed += 1
        else:
            print("❌ 模型管理器未正确加载")
        tests_total += 1
        
        # Test database manager
        print("测试数据库管理器...")
        from database_manager import get_database_manager
        db_manager = get_database_manager()
        if db_manager.is_loaded() and db_manager.is_indexed():
            print("✅ 数据库管理器正常")
            tests_passed += 1
        else:
            print("❌ 数据库管理器未正确加载")
        tests_total += 1
        
        # Test pose matcher
        print("测试姿态匹配引擎...")
        from pose_matching_engine import validate_matching_setup
        validation = validate_matching_setup()
        if validation['valid']:
            print("✅ 姿态匹配引擎正常")
            tests_passed += 1
        else:
            print(f"❌ 姿态匹配引擎错误: {validation['error']}")
        tests_total += 1
        
        # Test external APIs
        print("测试外部API连接...")
        from external_apis_manager import test_api_connection
        api_test = test_api_connection()
        if api_test.get('gemini_configured', False):
            print("✅ Gemini API已配置")
            if api_test.get('gemini_working', False):
                print("✅ Gemini API连接正常")
                tests_passed += 1
            else:
                print("⚠️ Gemini API已配置但连接失败")
        else:
            print("⚠️ Gemini API未配置")
        tests_total += 1
        
        print(f"\n🧪 测试完成: {tests_passed}/{tests_total} 通过")
        
        if tests_passed == tests_total:
            print("✅ 所有测试通过！系统准备就绪。")
            return True
        else:
            print("⚠️ 部分测试失败，但系统可能仍能正常运行。")
            return False
            
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        return False


def run_web_mode(args, components):
    """Run application in web server mode"""
    print(f"🌐 正在启动Web服务器模式 (端口: {args.port})...")
    
    try:
        # Import web components (these will be created in separate modules)
        from server_manager import start_server
        
        # Start the web server
        start_server(port=args.port, components=components)
        
    except ImportError:
        print("❌ Web服务器模块尚未实现。请稍后再试或使用 --mode cv。")
        sys.exit(1)
    except Exception as e:
        print(f"❌ Web服务器启动失败: {e}")
        if args.verbose:
            print(traceback.format_exc())
        sys.exit(1)


def run_opencv_mode(args, components):
    """Run application in OpenCV camera mode"""
    print("📷 正在启动OpenCV摄像头模式...")
    
    try:
        # Import OpenCV components
        from opencv_app import run_opencv_application
        
        # Start the OpenCV application
        run_opencv_application(components)
        
    except ImportError:
        print("❌ OpenCV应用模块尚未实现。请稍后再试或使用 --mode web。")
        sys.exit(1)
    except Exception as e:
        print(f"❌ OpenCV应用启动失败: {e}")
        if args.verbose:
            print(traceback.format_exc())
        sys.exit(1)


def cleanup_system():
    """Cleanup all system resources"""
    print("🧹 正在清理系统资源...")
    
    try:
        from model_manager import cleanup_models
        from database_manager import cleanup_database
        from human_processor import cleanup_human_processor
        from animal_processor import cleanup_animal_processor
        from pose_matching_engine import cleanup_pose_matcher
        from external_apis_manager import cleanup_apis_manager
        
        cleanup_models()
        cleanup_database()
        cleanup_human_processor()
        cleanup_animal_processor()
        cleanup_pose_matcher()
        cleanup_apis_manager()
        
        print("✅ 系统资源清理完成")
        
    except Exception as e:
        print(f"⚠️ 资源清理时出现错误: {e}")


def main():
    """Main application entry point"""
    print("🦁 动物姿态匹配器 - Animal Pose Matcher")
    print("=" * 50)
    
    try:
        # Parse command line arguments
        args = parse_arguments()
        
        if args.verbose:
            print(f"运行参数: {vars(args)}")
        
        # Validate paths
        path_errors = validate_paths(args)
        if path_errors:
            print("❌ 路径验证失败:")
            for error in path_errors:
                print(f"  - {error}")
            sys.exit(1)
        
        # Initialize system
        components = initialize_system(args)
        
        # Run tests if requested
        if args.test:
            success = run_system_tests()
            cleanup_system()
            sys.exit(0 if success else 1)
        
        # Choose mode and run
        if args.mode == 'web':
            run_web_mode(args, components)
        elif args.mode == 'cv':
            run_opencv_mode(args, components)
        else:
            print(f"❌ 未知模式: {args.mode}")
            sys.exit(1)
        
    except KeyboardInterrupt:
        print("\n⚡ 用户中断，正在退出...")
    except Exception as e:
        print(f"❌ 应用程序错误: {e}")
        if '--verbose' in sys.argv:
            print(traceback.format_exc())
        sys.exit(1)
    finally:
        cleanup_system()


if __name__ == "__main__":
    main() 