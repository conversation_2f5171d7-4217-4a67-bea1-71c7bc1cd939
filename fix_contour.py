#!/usr/bin/env python3
import re

# Original code pattern
original_pattern = r"""                                for \(let i = 0; i < contour\.length; i\+\+\) \{
                                    const point = contour\[i\]\[0\];
                                    const x = point\[0\] \* scaleX;[ ]*
                                    const y = point\[1\] \* scaleY;
                                    
                                    if \(i === 0\) \{
                                        overlayCtx\.moveTo\(x, y\);
                                    \} else \{
                                        overlayCtx\.lineTo\(x, y\);
                                    \}
                                \}"""

# Modified code
modified_code = """                                for (let i = 0; i < contour.length; i++) {
                                    const point = contour[i][0];
                                    const x = point[0] * scaleX;
                                    const y = point[1] * scaleY;
                                    
                                    // Get video element position offset
                                    const videoOffsetLeft = video.offsetLeft;
                                    const videoOffsetTop = video.offsetTop;
                                    
                                    // Add video position offset to coordinates
                                    const finalX = x + videoOffsetLeft;
                                    const finalY = y + videoOffsetTop;
                                    
                                    if (i === 0) {
                                        overlayCtx.moveTo(finalX, finalY);
                                    } else {
                                        overlayCtx.lineTo(finalX, finalY);
                                    }
                                }"""

# Read the file
with open('animal_pose_matcher.py', 'r') as f:
    content = f.read()

# Replace the pattern
modified_content = re.sub(original_pattern, modified_code, content)

# Write the modified content back to the file
with open('animal_pose_matcher.py', 'w') as f:
    f.write(modified_content)

print("File updated successfully!")
