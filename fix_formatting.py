#!/usr/bin/env python3

# Read the file
with open('animal_pose_matcher.py', 'r') as f:
    content = f.read()

# Fix the formatting issue
content = content.replace("const finalY = y + videoOffsetTop;                                    if", "const finalY = y + videoOffsetTop;\n                                    \n                                    if")

# Write the modified content back to the file
with open('animal_pose_matcher.py', 'w') as f:
    f.write(content)

print("Formatting fixed successfully!")
