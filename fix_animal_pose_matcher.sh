#!/bin/bash

# 备份原始文件
cp animal_pose_matcher.py animal_pose_matcher.py.bak

# 修改第一个函数中的代码
sed -i '' '3669,3678s/                        if (data.success \&\& data.contour) {\n                            \/\/ --- 修改: 移除冗余调用 ---\n                            \/\/ updateCanvasSize();\n\n                            \/\/ --- 添加调试日志 - 验证前端和后端尺寸是否匹配 ---\n                            console.log(`前端视频原始尺寸: ${video.videoWidth}x${video.videoHeight}`);\n                            console.log(`后端处理图像尺寸: ${data.image_width}x${data.image_height}`);\n                            if (video.videoWidth !== data.image_width || video.videoHeight !== data.image_height) {\n                                console.warn('\''警告: 前端视频尺寸与后端处理尺寸不匹配!'\'');\n                            }/                        if (data.success \&\& data.contour) {\n                            \/\/ 确保Canvas尺寸与视频显示尺寸同步\n                            updateCanvasSize();/' animal_pose_matcher.py

echo "修改完成，请检查文件"
