--- animal_pose_matcher.py.orig	2023-07-01 12:00:00.000000000 +0800
+++ animal_pose_matcher.py	2023-07-01 12:00:00.000000000 +0800
@@ -3575,13 +3575,19 @@
                                 for (let i = 0; i < contour.length; i++) {
                                     const point = contour[i][0];
                                     const x = point[0] * scaleX; 
                                     const y = point[1] * scaleY;
                                     
+                                    // Get video element position offset
+                                    const videoOffsetLeft = video.offsetLeft;
+                                    const videoOffsetTop = video.offsetTop;
+                                    
+                                    // Add video position offset to coordinates
+                                    const finalX = x + videoOffsetLeft;
+                                    const finalY = y + videoOffsetTop;
+                                    
                                     if (i === 0) {
-                                        overlayCtx.moveTo(x, y);
+                                        overlayCtx.moveTo(finalX, finalY);
                                     } else {
-                                        overlayCtx.lineTo(x, y);
+                                        overlayCtx.lineTo(finalX, finalY);
                                     }
                                 }
