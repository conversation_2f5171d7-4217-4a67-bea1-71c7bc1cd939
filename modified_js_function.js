// Modified startRealtimeContourDetection function
function startRealtimeContourDetection() {
    // Setup canvas
    function updateCanvasSize() {
        const videoRect = video.getBoundingClientRect();
        overlayCanvas.width = videoRect.width;
        overlayCanvas.height = videoRect.height;
    }
    
    updateCanvasSize();
    
    // Listen for video size changes
    new ResizeObserver(updateCanvasSize).observe(video);
    
    // Start realtime detection
    realtimeInterval = setInterval(() => {
        if (!video.paused && !video.ended) {
            const videoRect = video.getBoundingClientRect();
            
            const tempCanvas = document.createElement('canvas');
            tempCanvas.width = videoRect.width;
            tempCanvas.height = videoRect.height;
            
            const ctx = tempCanvas.getContext('2d');
            ctx.drawImage(video, 0, 0, tempCanvas.width, tempCanvas.height);
            
            const imageData = tempCanvas.toDataURL('image/jpeg', 0.7);
            
            fetch('/contour-only', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    image: imageData
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success && data.contour) {
                    updateCanvasSize();
                    
                    const overlayCtx = overlayCanvas.getContext('2d');
                    overlayCtx.clearRect(0, 0, overlayCanvas.width, overlayCanvas.height);
                    
                    const contour = JSON.parse(data.contour);
                    
                    overlayCtx.strokeStyle = 'green';
                    overlayCtx.lineWidth = 3;
                    overlayCtx.beginPath();
                    
                    if (contour.length > 0) {
                        const originalWidth = data.image_width;
                        const originalHeight = data.image_height;
                        const bbox = data.bbox || [0, 0, originalWidth, originalHeight];
                        
                        const scaleX = overlayCanvas.width / originalWidth;
                        const scaleY = overlayCanvas.height / originalHeight;
                        
                        for (let i = 0; i < contour.length; i++) {
                            const point = contour[i][0];
                            const x = point[0] * scaleX;
                            const y = point[1] * scaleY;
                            
                            // Get video element position offset
                            const videoOffsetLeft = video.offsetLeft;
                            const videoOffsetTop = video.offsetTop;
                            
                            // Add video position offset to coordinates
                            const finalX = x + videoOffsetLeft;
                            const finalY = y + videoOffsetTop;
                            
                            if (i === 0) {
                                overlayCtx.moveTo(finalX, finalY);
                            } else {
                                overlayCtx.lineTo(finalX, finalY);
                            }
                        }
                        
                        overlayCtx.closePath();
                        overlayCtx.stroke();
                    }
                }
            })
            .catch(error => {
                console.error('Error getting contour:', error);
            });
        }
    }, 200);
}
