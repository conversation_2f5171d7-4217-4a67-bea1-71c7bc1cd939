// 这是一个修复脚本，用于恢复轮廓显示功能

// 在第一个 startRealtimeContourDetection 函数中
function updateFirstFunction() {
    // 1. 使用视频的原始尺寸而不是显示尺寸
    const tempCanvas = document.createElement('canvas');
    tempCanvas.width = video.videoWidth;
    tempCanvas.height = video.videoHeight;
    
    const ctx = tempCanvas.getContext('2d');
    ctx.drawImage(video, 0, 0, video.videoWidth, video.videoHeight);
    
    // 2. 在回调中恢复 updateCanvasSize() 调用
    if (data.success && data.contour) {
        // 确保Canvas尺寸与视频显示尺寸同步
        updateCanvasSize();
        
        const overlayCtx = overlayCanvas.getContext('2d');
        overlayCtx.clearRect(0, 0, overlayCanvas.width, overlayCanvas.height);
        
        // 其余代码保持不变...
    }
}

// 在第二个 startRealtimeContourDetection 函数中
function updateSecondFunction() {
    // 1. 使用视频的原始尺寸而不是显示尺寸
    const tempCanvas = document.createElement('canvas');
    tempCanvas.width = video.videoWidth;
    tempCanvas.height = video.videoHeight;
    
    const ctx = tempCanvas.getContext('2d');
    ctx.drawImage(video, 0, 0, video.videoWidth, video.videoHeight);
    
    // 2. 在回调中恢复 updateCanvasSize() 调用
    if (data.success && data.contour) {
        // 确保Canvas尺寸与视频显示尺寸同步
        updateCanvasSize();
        
        const overlayCtx = overlayCanvas.getContext('2d');
        overlayCtx.clearRect(0, 0, overlayCanvas.width, overlayCanvas.height);
        
        // 其余代码保持不变...
    }
}

// 注意：这个脚本只是一个参考，不会直接执行
console.log("请按照这个脚本中的指导修改 animal_pose_matcher.py 文件");
